"""
高级验证和存在性检查框架 (函数式重构版 v4 - Pylance 完全兼容)
提供基于 returns 库的链式验证、自动错误处理和简化的API
支持完整的CRUD操作、智能列表处理和两层错误防护

修正版本：解决了类型安全、错误处理和逻辑问题
"""

from __future__ import annotations

import logging
from functools import wraps
from typing import (
    Any,
    Callable,
    Dict,
    Generic,
    List,
    Optional,
    Tuple,
    TypeVar,
    Union,
    cast,
)

from bson import ObjectId
from bson.errors import InvalidDocument, InvalidId
from flask import Response, jsonify, request
from pymongo import ReturnDocument
from pymongo.collection import Collection
from pymongo.errors import (
    AutoReconnect,
    BulkWriteError,
    ConfigurationError,
    ConnectionFailure,
    DuplicateKeyError,
    InvalidOperation,
    NetworkTimeout,
    OperationFailure,
    ServerSelectionTimeoutError,
    WriteConcernError,
    WriteError,
)
from pymongo.mongo_client import MongoClient
from returns.pipeline import is_successful
from returns.primitives.exceptions import UnwrapFailedError
from returns.result import Failure, Result, Success, safe
from dataclasses import dataclass

# --- 1. 设置与核心类型定义 ---

logger = logging.getLogger(__name__)

T = TypeVar("T")
T_val = TypeVar("T_val")  # 为 Validator 中的值创建的类型变量
U = TypeVar("U")  # 用于函数签名的通用类型变量

Document = Dict[str, Any]
ApiResponse = Tuple[Response, int]
F = Callable[..., ApiResponse]
ValidatorState = Dict[str, Any]  # Validator 类的内部状态结构


@dataclass
class CustomUpdateResult:
    """用于 update_many 的自定义返回类型"""

    matched_count: int
    modified_count: int


@dataclass
class CustomDeleteResult:
    """用于 delete 操作的自定义返回类型"""

    deleted_count: int


# --- 2. 核心异常类 ---
class ValidationError(Exception):
    def __init__(
        self,
        status: str = "error",
        message: str = "验证失败",
        code: str = "VALIDATION_ERROR",
        data: Any = None,
        status_code: int = 400,
    ):
        self.status = status
        self.message = message
        self.code = code
        self.data = data
        self.status_code = status_code
        super().__init__(f"[{code}] - {message} - {status_code}")

    def to_response(self) -> ApiResponse:
        return (
            jsonify(
                {
                    "status": self.status,
                    "message": self.message,
                    "code": self.code,
                    "data": self.data,
                }
            ),
            self.status_code,
        )


# --- 3. 链式查询与验证核心 (修正版) ---
class QueryResult(Generic[T]):
    def __init__(self, result: Result[T, ValidationError]):
        self._result = result

    def _apply_to_value(
        self, func: Callable[[Any], Result[U, ValidationError]]
    ) -> Result[Union[U, List[U]], ValidationError]:
        if not is_successful(self._result):
            return cast(Result[Union[U, List[U]], ValidationError], self._result)

        current_value = self._result.unwrap()

        if isinstance(current_value, list):
            if not current_value:
                return Success([])

            results = [func(item) for item in current_value]
            # 修正：检查是否有失败的结果
            for result in results:
                if not is_successful(result):
                    return Failure(result.failure())
            return Success([r.unwrap() for r in results])
        else:
            return cast(Result[Union[U, List[U]], ValidationError], func(current_value))

    def field(
        self, field_name: str, error_message: Optional[str] = None
    ) -> QueryResult[Any]:
        """获取文档中的字段值

        :param field_name: 字段名
        :param error_message: 自定义错误信息，默认为"字段 {field_name} 不存在"

        :return QueryResult: 包含字段值的 QueryResult 对象

        :raise ValidationError: 当字段不存在时抛出，包含错误信息和404状态码
        """

        def _get_field(doc: Document) -> Result[Any, ValidationError]:
            if not isinstance(doc, dict):
                return Failure(
                    ValidationError(
                        "error",
                        f"尝试从非字典对象获取字段 '{field_name}'",
                        "INVALID_DOCUMENT_TYPE",
                        None,
                        400,
                    )
                )

            value = doc.get(field_name)
            # 检查字段是否存在
            if value is None:
                msg = error_message or f"字段 '{field_name}' 在文档中不存在"
                return Failure(
                    ValidationError("error", msg, "FIELD_NOT_FOUND", None, 404)
                )
            return Success(value)

        # 修正：正确处理绑定操作
        if not is_successful(self._result):
            return QueryResult(cast(Result[Any, ValidationError], self._result))

        new_result = self._apply_to_value(_get_field)
        return QueryResult(new_result)

    def not_empty(
        self, error_message: Optional[str] = None, status: str = "error"
    ) -> QueryResult[T]:
        def _check_not_empty(value: Any) -> Result[Any, ValidationError]:
            is_empty = (
                value is None
                or (isinstance(value, str) and not value.strip())
                or (isinstance(value, (list, dict)) and not value)
            )
            if is_empty:
                msg = error_message or "值不能为空"
                return Failure(ValidationError(status, msg, "VALUE_EMPTY", value, 400))
            return Success(value)

        if not is_successful(self._result):
            return QueryResult(cast(Result[T, ValidationError], self._result))

        new_result = self._apply_to_value(_check_not_empty)
        return QueryResult(cast(Result[T, ValidationError], new_result))

    def transform(self, transformer: Callable[[Any], Any]) -> QueryResult[Any]:
        def _apply_transform(value: Any) -> Any:
            if isinstance(value, list):
                return [transformer(item) for item in value]
            return transformer(value)

        return QueryResult(self._result.map(_apply_transform))

    def unwrap(self) -> T:
        """
        如果验证成功，则返回值；如果验证失败，则抛出异常。

        :return T: 验证通过的值

        :raise ValidationError: 当验证失败时抛出
        """
        return self._result.unwrap()

    def unwrap_or(self, default: Any) -> Any:
        """
        如果验证成功，则返回值；如果验证失败或值不存在，则返回指定的默认值。

        :param default: 默认值

        :return Any: 验证通过的值或默认值
        """
        return self._result.value_or(default)

    def result(self) -> Result[T, ValidationError]:
        """
        获取内部的 Result 对象，用于更高级的错误处理或链式调用

        :return Result[T, ValidationError]: 内部的 Result 对象
        """
        return self._result


class DatabaseHelper:
    def __init__(self, collection: Collection[Document]):
        self.collection = collection

    def _handle_db_error(self, exception: Exception) -> ValidationError:
        """将数据库异常转换为 ValidationError"""
        if isinstance(exception, (ServerSelectionTimeoutError, NetworkTimeout)):
            return ValidationError(
                "error", "数据库连接超时", "DATABASE_TIMEOUT", None, 503
            )
        elif isinstance(exception, ConnectionFailure):
            return ValidationError(
                "error", "数据库连接失败", "DATABASE_CONNECTION_ERROR", 503
            )
        elif isinstance(exception, DuplicateKeyError):
            return ValidationError(
                "error", "数据已存在", "DUPLICATE_KEY_ERROR", None, 409
            )
        elif isinstance(exception, (WriteError, BulkWriteError)):
            return ValidationError("error", "数据写入失败", "WRITE_ERROR", None, 400)
        elif isinstance(exception, InvalidDocument):
            return ValidationError(
                "error", "文档格式无效", "INVALID_DOCUMENT", None, 400
            )
        elif isinstance(exception, InvalidId):
            return ValidationError("error", "ID格式无效", "INVALID_ID", None, 400)
        else:
            return ValidationError(
                "error",
                f"数据库操作失败: {str(exception)}",
                "DB_OPERATION_ERROR",
                None,
                500,
            )

    # --- Read Operations ---
    def find_one(
        self,
        query: Document,
        sort: Optional[List[Tuple[str, int]]] = None,
        projection: Optional[Document] = None,
        error_message: str = "记录不存在",
    ) -> QueryResult[Document]:
        @safe
        def _do_find() -> Optional[Document]:
            return self.collection.find_one(query, projection=projection, sort=sort)

        result: Result[Optional[Document], Exception] = _do_find()

        # 修正：先用 .alt() 将 Exception 转换为 ValidationError，再用 .bind() 处理业务逻辑
        final_result: Result[Document, ValidationError] = result.alt(
            self._handle_db_error
        ).bind(  # 步骤1: 任何DB异常都变成 ValidationError  # 步骤2: 现在错误类型统一了，可以安全地 bind
            lambda doc: (
                Success(doc)
                if doc is not None
                else Failure(
                    ValidationError(
                        "error", error_message, "RECORD_NOT_FOUND", None, 404
                    )
                )
            )
        )

        return QueryResult(final_result)

    def find(
        self,
        query: Optional[Document] = None,
        projection: Optional[Document] = None,
        sort: Optional[List[Tuple[str, int]]] = None,
        limit: Optional[int] = None,
        skip: Optional[int] = None,
        error_message: str = "没有找到任何记录",  # 新增 error_message 参数
    ) -> QueryResult[List[Document]]:
        # 一个都找不到的时候，返回一个空列表，
        @safe
        def _do_find() -> List[Document]:
            cursor = self.collection.find(query, projection)
            if sort:
                cursor = cursor.sort(sort)
            if skip is not None:
                cursor = cursor.skip(skip)
            if limit:
                cursor = cursor.limit(limit)
            return list(cursor)

        result: Result[List[Document], Exception] = _do_find()

        # 采用与 find_one 完全相同的逻辑模式
        final_result: Result[List[Document], ValidationError] = result.alt(
            self._handle_db_error
        ).bind(  # 步骤1: 捕获并转换数据库执行异常  # 步骤2: 处理业务逻辑（检查是否为空）
            lambda doc_list: (
                Success(doc_list)
                if doc_list  # 检查列表是否非空
                else Failure(
                    ValidationError(
                        "error", error_message, "RECORDS_NOT_FOUND", None, 404
                    )
                )
            )
        )

        return QueryResult(final_result)

        # --- Create Operations ---

    # --- 新增的 aggregate 方法 ---
    def aggregate(
        self,
        pipeline: List[Document],
        error_message: str = "聚合操作未返回任何结果",
    ) -> QueryResult[List[Document]]:
        """
        执行 MongoDB 聚合管道。
        如果结果为空列表，将返回一个 Failure。

        :param pipeline: 聚合管道阶段的列表。
        :param error_message: 当聚合结果为空时返回的错误信息。
        :return: 包含聚合结果列表的 QueryResult 对象。
        """

        @safe
        def _do_aggregate() -> List[Document]:
            # 聚合操作返回一个游标，我们将其立即转换为列表
            return list(self.collection.aggregate(pipeline))

        result: Result[List[Document], Exception] = _do_aggregate()

        # 采用与 find 相同的模式来处理错误和空结果
        final_result: Result[List[Document], ValidationError] = result.alt(
            self._handle_db_error
        ).bind(  # 步骤1: 将任何数据库异常转换为 ValidationError
            lambda doc_list: (  # 步骤2: 处理业务逻辑（检查列表是否为空）
                Success(doc_list)
                if doc_list  # 如果列表非空，则为成功
                else Failure(
                    ValidationError(
                        "error", error_message, "AGGREGATION_NO_RESULT", None, 404
                    )
                )
            )
        )

        return QueryResult(final_result)

    # --- 结束新增 ---

    def insert_one(self, document: Document) -> QueryResult[Document]:
        """
        插入一个文档并返回插入后的文档。

        自动检验 `acknowledged`
            1. 如果 `acknowledged` 为 `True`，则返回插入文档的 `_id`。
            2. 如果 `acknowledged` 为 `False`, 构建一个 `ValidationError`。

        :param document: 要插入的文档。
        :return: 包含插入后的文档的 QueryResult 对象。
        """

        @safe
        def _do_insert() -> Tuple[Document, bool]:
            doc_copy = document.copy()
            result = self.collection.insert_one(doc_copy)
            doc_copy["_id"] = result.inserted_id
            return doc_copy, result.acknowledged

        result: Result[Tuple[Document, bool], Exception] = _do_insert()

        final_result: Result[Document, ValidationError] = result.alt(
            self._handle_db_error
        ).bind(
            lambda data: (
                Success(data[0])
                if data[1]  # acknowledged
                else Failure(
                    ValidationError(
                        "error",
                        "插入操作未被确认",
                        "INSERT_NOT_ACKNOWLEDGED",
                        None,
                        500,
                    )
                )
            )
        )

        return QueryResult(final_result)

    # --- Update Operations ---
    def update_one(
        self,
        query: Document,
        update: Document,
        upsert: bool = False,
        error_if_none_matched: bool = False,
    ) -> QueryResult[CustomUpdateResult]:
        """
        更新单个文档并返回更新结果的对象。
        此行为与 `pymongo` 原生的 `update_one` 逻辑一致。
        默认情况下，即使没有文档被匹配或修改，操作也视为成功。

        :param query: 查询条件。
        :param update: 更新操作。
        :param upsert: 如果为 `True`，则在没有匹配文档时插入新文档。
        :param error_if_none_matched: 如果为 `True`，并且没有文档匹配查询条件（且 `upsert=False`），则返回 `Failure`。
        :return: 包含 `.matched_count` 和 `.modified_count` 属性的 `QueryResult` 对象。
        """

        @safe
        def _do_update() -> CustomUpdateResult:
            result = self.collection.update_one(query, update, upsert=upsert)
            return CustomUpdateResult(
                matched_count=result.matched_count,
                modified_count=result.modified_count,
            )

        result: Result[CustomUpdateResult, Exception] = _do_update()

        # 如果调用者不要求在“未找到”时报错，则直接返回结果（转换DB异常后）
        if not error_if_none_matched:
            return QueryResult(result.alt(self._handle_db_error))

        # 如果要求在未匹配时报错，则使用 bind 检查匹配计数
        final_result: Result[CustomUpdateResult, ValidationError] = result.alt(
            self._handle_db_error
        ).bind(
            lambda res_obj: (
                # 如果有文档匹配，或即使不匹配但通过 upsert 插入了新文档，都视为成功
                Success(res_obj)
                if res_obj.matched_count > 0 or upsert
                else Failure(
                    ValidationError(
                        "error",
                        "没有找到要更新的记录",
                        "UPDATE_TARGET_NOT_FOUND",
                        None,
                        404,
                    )
                )
            )
        )
        return QueryResult(final_result)

    def update_many(
        self,
        query: Document,
        update: Document,
        upsert: bool = False,
        error_if_none_matched: bool = False,
    ) -> QueryResult[CustomUpdateResult]:
        """
        更新多个文档并返回更新结果的对象。
        注意：当 upsert=True 且没有文档匹配时，MongoDB 只会插入一个新文档。

        :param query: 查询条件。
        :param update: 更新操作。
        :param upsert: 如果为 True，则在没有匹配文档时插入一个新文档。
        :param error_if_none_matched: 如果为 True，并且没有文档匹配查询条件（且 upsert=False），则返回 Failure。
        :return: 包含 .matched_count 和 .modified_count 属性的 QueryResult 对象。
        """

        @safe
        def _do_update_many() -> CustomUpdateResult:
            result = self.collection.update_many(query, update, upsert=upsert)
            return CustomUpdateResult(
                matched_count=result.matched_count,
                modified_count=result.modified_count,
            )

        result: Result[CustomUpdateResult, Exception] = _do_update_many()

        # 如果调用者不要求在“未找到”时报错，则直接返回结果
        if not error_if_none_matched:
            return QueryResult(result.alt(self._handle_db_error))

        # 如果要求在未匹配时报错，则使用 bind 检查匹配计数
        final_result: Result[CustomUpdateResult, ValidationError] = result.alt(
            self._handle_db_error
        ).bind(
            lambda res_obj: (
                # 如果有文档匹配，或即使不匹配但启用了 upsert，都视为成功
                Success(res_obj)
                if res_obj.matched_count > 0 or upsert
                else Failure(
                    ValidationError(
                        "error",
                        "没有找到任何要更新的记录",
                        "UPDATE_TARGETS_NOT_FOUND",  # 使用复数形式以区分
                        None,
                        404,
                    )
                )
            )
        )
        return QueryResult(final_result)

    # --- Delete Operations ---
    def delete_one(
        self,
        query: Document,
        error_if_none_deleted: bool = False,
    ) -> QueryResult[CustomDeleteResult]:
        """
        删除单个文档并返回删除的数量。
        此行为与 `pymongo` 原生的 `delete_one` 逻辑一致。
        默认情况下，即使删除数量为0也视为成功。

        :param query: 查询条件
        :param error_if_none_deleted: 如果为`True`，则在删除数量为0时返回 `Failure`。
        :return: 包含 `deleted_count` 的 `QueryResult` 对象 (0 或 1)。
        """

        @safe
        def _do_delete() -> CustomDeleteResult:
            result = self.collection.delete_one(query)
            return CustomDeleteResult(deleted_count=result.deleted_count)

        result: Result[CustomDeleteResult, Exception] = _do_delete()

        # 如果调用者不要求在“未找到”时报错，则直接返回结果（转换DB异常后）
        if not error_if_none_deleted:
            return QueryResult(result.alt(self._handle_db_error))

        # 如果要求在删除0个时报错，则使用 bind 检查删除计数
        final_result: Result[CustomDeleteResult, ValidationError] = result.alt(
            self._handle_db_error
        ).bind(
            lambda res_obj: (
                Success(res_obj)
                if res_obj.deleted_count > 0
                else Failure(
                    ValidationError(
                        "error",
                        "要删除的记录不存在",
                        "DELETE_TARGET_NOT_FOUND",
                        None,
                        404,
                    )
                )
            )
        )
        return QueryResult(final_result)

    def delete_many(
        self,
        query: Document,
        error_if_none_deleted: bool = False,
    ) -> QueryResult[CustomDeleteResult]:
        """
        删除多个文档并返回删除的数量。
        默认情况下，即使删除数量为0也视为成功。
        如果 error_if_none_deleted=True，则在删除数量为0时返回 Failure。
        """

        @safe
        def _do_delete_many() -> CustomDeleteResult:
            result = self.collection.delete_many(query)
            return CustomDeleteResult(deleted_count=result.deleted_count)

        result: Result[CustomDeleteResult, Exception] = _do_delete_many()

        # 对于 delete_many，"删除0个" 通常是可接受的成功状态
        # 但我们提供一个开关，让调用者可以要求它在删除0个时失败
        if not error_if_none_deleted:
            return QueryResult(result.alt(self._handle_db_error))

        # 如果要求在删除0个时报错，则采用 bind 模式
        final_result: Result[CustomDeleteResult, ValidationError] = result.alt(
            self._handle_db_error
        ).bind(
            lambda res_obj: (
                Success(res_obj)
                if res_obj.deleted_count > 0
                else Failure(
                    ValidationError(
                        "error",
                        "没有找到任何要删除的记录",
                        "NO_RECORDS_TO_DELETE",
                        None,
                        404,
                    )
                )
            )
        )
        return QueryResult(final_result)

    def drop(self) -> QueryResult[Dict[str, str]]:
        """
        删除（Drop）整个集合。

        这是一个破坏性操作，一旦执行无法撤销。

        :return: 如果操作成功，返回一个包含成功信息的 QueryResult 对象。
                 如果失败，返回一个包含 ValidationError 的 Failure 对象。
        """

        @safe
        def _do_drop() -> None:
            """执行 Pymongo 的 drop 命令"""
            self.collection.drop()

        # 执行被 @safe 包装的函数
        result: Result[None, Exception] = _do_drop()

        # 链式处理结果:
        # 1. 使用 .alt() 将任何底层的数据库异常转换为我们自定义的 ValidationError。
        # 2. 如果操作成功 (结果为 Success(None))，
        #    使用 .map() 将结果映射为一个更具信息量的成功消息字典。
        final_result: Result[Dict[str, str], ValidationError] = result.alt(
            self._handle_db_error
        ).map(lambda _: {"message": f"集合 '{self.collection.name}' 已被成功删除。"})

        # 将最终的 Result 对象包装在 QueryResult 容器中并返回
        return QueryResult(final_result)


# --- 4. 独立的链式验证器 ---
class Validator(Generic[T_val]):
    def __init__(self, result: Result[ValidatorState, ValidationError]):
        self._result: Result[ValidatorState, ValidationError] = result

    @staticmethod
    def of(value: T_val, context: str = "") -> Validator[T_val]:
        state: ValidatorState = {"value": value, "context": context}
        return Validator(Success(state))

    def required(self, message: Optional[str] = None) -> Validator[T_val]:
        def _check(data: ValidatorState) -> Result[ValidatorState, ValidationError]:
            if data["value"] is None:
                msg = message or f"{data.get('context', 'Value')}不能缺失"
                return Failure(
                    ValidationError("error", msg, "REQUIRED_FIELD_MISSING", None, 400)
                )
            return Success(data)

        self._result = self._result.bind(_check)
        return self

    def not_empty(
        self, message: Optional[str] = None, status: str = "error"
    ) -> Validator[T_val]:
        def _check(data: ValidatorState) -> Result[ValidatorState, ValidationError]:
            value = data["value"]
            if (isinstance(value, str) and not value.strip()) or (
                isinstance(value, (list, dict)) and not value
            ):
                msg = message or f"{data.get('context', 'Value')}不能为空"
                return Failure(ValidationError(status, msg, "EMPTY_VALUE", None, 400))
            return Success(data)

        self._result = self._result.bind(_check)
        return self

    def is_valid_object_id(self, message: Optional[str] = None) -> Validator[T_val]:
        def _check(data: ValidatorState) -> Result[ValidatorState, ValidationError]:
            try:
                ObjectId(str(data["value"]))
                return Success(data)
            except (InvalidId, TypeError, ValueError):
                msg = message or f"{data.get('context', 'Value')}不是有效的ObjectId格式"
                return Failure(
                    ValidationError("error", msg, "INVALID_OBJECT_ID", None, 400)
                )

        self._result = self._result.bind(_check)
        return self

    def unwrap(self) -> T_val:
        return self._result.map(lambda data: data["value"]).unwrap()

    def unwrap_or(self, default: Any) -> Any:
        return self._result.map(lambda data: data["value"]).value_or(default)

    def is_successful(self) -> bool:
        """检查验证是否成功"""
        return is_successful(self._result)


# --- 5. 便捷函数与请求验证器 ---
def validate(value: T_val, context: str = "") -> Validator[T_val]:
    return Validator.of(value, context)


def get_collection(
    client: MongoClient[Document], db_name: str, collection_name: str
) -> DatabaseHelper:
    database = client[db_name]
    collection: Collection[Document] = database[collection_name]
    return DatabaseHelper(collection=collection)


class RequestValidator:
    @staticmethod
    def get_json_unwrap() -> Dict[str, Any]:
        """获取并验证请求体是有效的JSON对象。"""
        try:
            data = request.get_json(
                force=True
            )  # 修正：使用 force=True 以更好地处理错误
        except Exception:
            raise ValidationError(
                "error", "请求体必须是有效的JSON格式", "INVALID_JSON_FORMAT", None, 400
            )

        if not isinstance(data, dict):
            raise ValidationError(
                "error",
                "请求体必须是有效的JSON对象格式",
                "INVALID_REQUEST_BODY",
                None,
                400,
            )
        return data

    @staticmethod
    def extract_and_validate(data: Dict[str, Any], *fields: str) -> Dict[str, Any]:
        validated_data: Dict[str, Any] = {}
        for field in fields:
            value = data.get(field)
            validator = validate(value, field).required().not_empty()
            if not validator.is_successful():
                # 如果验证失败，unwrap 会抛出异常
                pass
            validated_data[field] = validator.unwrap()
        return validated_data


# --- 6. 装饰器 ---
def handle_api_exceptions(info: str = "") -> Callable[[F], F]:
    def decorator(f: F) -> F:
        @wraps(f)
        def decorated_function(*args: Any, **kwargs: Any) -> ApiResponse:
            try:
                return f(*args, **kwargs)
            except UnwrapFailedError as e:
                # 修正：正确处理 UnwrapFailedError
                try:
                    # 尝试获取原始错误
                    if hasattr(e, "halted_container") and hasattr(
                        e.halted_container, "failure"
                    ):
                        original_error = e.halted_container.failure()
                        if isinstance(original_error, ValidationError):
                            logger.warning(f"Validation chain failed: {original_error}")
                            return original_error.to_response()

                    # 如果无法获取原始错误，创建一个通用错误
                    error_info = f"{info}验证失败" if info else "验证失败"
                    logger.error(f"{error_info}: {e}", exc_info=True)
                    return (
                        jsonify(
                            {
                                "status": "error",
                                "message": error_info,
                                "code": "VALIDATION_FAILED",
                                "data": None,
                            }
                        ),
                        400,
                    )

                except Exception as inner_e:
                    logger.error(
                        f"Error handling UnwrapFailedError: {inner_e}", exc_info=True
                    )
                    return (
                        jsonify(
                            {
                                "status": "error",
                                "message": "内部错误",
                                "code": "INTERNAL_ERROR",
                                "data": None,
                            }
                        ),
                        500,
                    )

            except ValidationError as e:
                logger.warning(f"Validation failed: {e}")
                return e.to_response()
            except Exception as e:
                error_info = f"{info}失败" if info else "服务器内部错误"
                logger.error(f"{error_info}: {e}", exc_info=True)
                return (
                    jsonify(
                        {
                            "status": "error",
                            "message": error_info + str(e),
                            "code": "INTERNAL_ERROR",
                            "data": None,
                        }
                    ),
                    500,
                )

        return cast(F, decorated_function)

    return decorator


def handle_database_exceptions(f: F) -> F:
    """数据库异常处理装饰器"""

    @wraps(f)
    def decorated_function(*args: Any, **kwargs: Any) -> ApiResponse:
        try:
            return f(*args, **kwargs)
        except (ServerSelectionTimeoutError, NetworkTimeout, AutoReconnect) as e:
            logger.error(f"数据库网络错误: {e}")
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": "数据库连接超时",
                        "code": "DATABASE_TIMEOUT",
                        "data": None,
                    }
                ),
                503,
            )
        except ConnectionFailure as e:
            logger.error(f"数据库连接失败: {e}")
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": "数据库服务不可用",
                        "code": "DATABASE_CONNECTION_ERROR",
                        "data": None,
                    }
                ),
                503,
            )
        except DuplicateKeyError as e:
            logger.warning(f"数据重复错误: {e}")
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": "数据已存在，不能重复创建",
                        "code": "DUPLICATE_KEY_ERROR",
                        "data": None,
                    }
                ),
                409,
            )
        except (WriteError, BulkWriteError) as e:
            logger.error(f"数据库写入错误: {e}")
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": "数据写入失败",
                        "code": "WRITE_ERROR",
                        "data": None,
                    }
                ),
                400,
            )
        except WriteConcernError as e:
            logger.error(f"数据库写入关注错误: {e}")
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": "数据写入确认失败",
                        "code": "WRITE_CONCERN_ERROR",
                        "data": None,
                    }
                ),
                500,
            )
        except OperationFailure as e:
            logger.error(f"数据库操作失败: {e}")
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": "数据库操作失败",
                        "code": "OPERATION_FAILURE",
                        "data": None,
                    }
                ),
                500,
            )
        except InvalidDocument as e:
            logger.error(f"无效文档格式: {e}")
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": "提交的数据格式错误",
                        "code": "INVALID_DOCUMENT",
                        "data": None,
                    }
                ),
                400,
            )
        except InvalidId as e:
            logger.error(f"无效ID格式: {e}")
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": "ID格式错误",
                        "code": "INVALID_ID",
                        "data": None,
                    }
                ),
                400,
            )
        except (ConfigurationError, InvalidOperation) as e:
            logger.error(f"数据库配置或操作错误: {e}")
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": "数据库配置或操作无效",
                        "code": "DATABASE_CONFIG_ERROR",
                        "data": None,
                    }
                ),
                500,
            )

    return cast(F, decorated_function)


def validate_request(*required_fields: str) -> Callable[[F], F]:
    """请求验证装饰器

    确定请求体是否为正确的json格式，并确认是否包含所有必需字段
    """

    def decorator(f: F) -> F:
        @wraps(f)
        def decorated_function(*args: Any, **kwargs: Any) -> ApiResponse:
            data = RequestValidator.get_json_unwrap()
            validated_data = RequestValidator.extract_and_validate(
                data, *required_fields
            )
            # kwargs["validated"] = validated_data
            kwargs.update(validated_data)
            kwargs["data"] = data
            return f(*args, **kwargs)

        return cast(F, decorated_function)

    return decorator
