{"data_mtime": 1752108653, "dep_lines": [46, 27, 30, 31, 44, 45, 47, 9, 11, 12, 13, 26, 28, 29, 48, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["returns.primitives.exceptions", "bson.errors", "pymongo.collection", "pymongo.errors", "pymongo.mongo_client", "returns.pipeline", "returns.result", "__future__", "logging", "functools", "typing", "bson", "flask", "pymongo", "dataclasses", "builtins", "_frozen_importlib", "_typeshed", "abc", "bson.objectid", "bson.raw_bson", "flask.globals", "flask.json", "flask.wrappers", "pymongo.collation", "pymongo.common", "pymongo.results", "pymongo.synchronous", "pymongo.synchronous.client_session", "pymongo.synchronous.collection", "pymongo.synchronous.command_cursor", "pymongo.synchronous.cursor", "pymongo.synchronous.database", "pymongo.synchronous.mongo_client", "returns", "returns.interfaces", "returns.interfaces.altable", "returns.interfaces.applicative", "returns.interfaces.bimappable", "returns.interfaces.bindable", "returns.interfaces.container", "returns.interfaces.equable", "returns.interfaces.failable", "returns.interfaces.lashable", "returns.interfaces.mappable", "returns.interfaces.specific", "returns.interfaces.specific.result", "returns.interfaces.swappable", "returns.interfaces.unwrappable", "returns.primitives", "returns.primitives.container", "returns.primitives.hkt", "returns.primitives.laws", "returns.primitives.types", "types", "typing_extensions", "werkzeug", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.sansio.response", "werkzeug.wrappers", "werkzeug.wrappers.request", "werkzeug.wrappers.response"], "hash": "fa0f9b14f85bdf10a257c468a1c1abf896bc3203", "id": "validator.validator_framework", "ignore_all": false, "interface_hash": "29f1a70a090eff06926b39d5b016a3bfe5ff6c7a", "mtime": 1752114432, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\validator\\validator_framework.py", "plugin_data": null, "size": 36624, "suppressed": [], "version_id": "1.15.0"}