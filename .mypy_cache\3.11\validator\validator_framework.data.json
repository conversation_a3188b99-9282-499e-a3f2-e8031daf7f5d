{".class": "MypyFile", "_fullname": "validator.validator_framework", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ApiResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "validator.validator_framework.ApiResponse", "line": 59, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["flask.wrappers.Response", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "AutoReconnect": {".class": "SymbolTableNode", "cross_ref": "pymongo.errors.AutoReconnect", "kind": "Gdef"}, "BulkWriteError": {".class": "SymbolTableNode", "cross_ref": "pymongo.errors.BulkWriteError", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Collection": {".class": "SymbolTableNode", "cross_ref": "pymongo.synchronous.collection.Collection", "kind": "Gdef"}, "ConfigurationError": {".class": "SymbolTableNode", "cross_ref": "pymongo.errors.ConfigurationError", "kind": "Gdef"}, "ConnectionFailure": {".class": "SymbolTableNode", "cross_ref": "pymongo.errors.ConnectionFailure", "kind": "Gdef"}, "CustomDeleteResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "validator.validator_framework.CustomDeleteResult", "name": "CustomDeleteResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "validator.validator_framework.CustomDeleteResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 76, "name": "deleted_count", "type": "builtins.int"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "validator.validator_framework", "mro": ["validator.validator_framework.CustomDeleteResult", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "validator.validator_framework.CustomDeleteResult.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "deleted_count"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.CustomDeleteResult.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "deleted_count"], "arg_types": ["validator.validator_framework.CustomDeleteResult", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CustomDeleteResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "validator.validator_framework.CustomDeleteResult.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "deleted_count"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["deleted_count"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "validator.validator_framework.CustomDeleteResult.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["deleted_count"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CustomDeleteResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "validator.validator_framework.CustomDeleteResult.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["deleted_count"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CustomDeleteResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "deleted_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "validator.validator_framework.CustomDeleteResult.deleted_count", "name": "deleted_count", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.CustomDeleteResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "validator.validator_framework.CustomDeleteResult", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CustomUpdateResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "validator.validator_framework.CustomUpdateResult", "name": "CustomUpdateResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "validator.validator_framework.CustomUpdateResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 68, "name": "matched_count", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 69, "name": "modified_count", "type": "builtins.int"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "validator.validator_framework", "mro": ["validator.validator_framework.CustomUpdateResult", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "validator.validator_framework.CustomUpdateResult.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "matched_count", "modified_count"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.CustomUpdateResult.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "matched_count", "modified_count"], "arg_types": ["validator.validator_framework.CustomUpdateResult", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CustomUpdateResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "validator.validator_framework.CustomUpdateResult.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "matched_count"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "modified_count"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["matched_count", "modified_count"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "validator.validator_framework.CustomUpdateResult.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["matched_count", "modified_count"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CustomUpdateResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "validator.validator_framework.CustomUpdateResult.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["matched_count", "modified_count"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CustomUpdateResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "matched_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "validator.validator_framework.CustomUpdateResult.matched_count", "name": "matched_count", "type": "builtins.int"}}, "modified_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "validator.validator_framework.CustomUpdateResult.modified_count", "name": "modified_count", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.CustomUpdateResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "validator.validator_framework.CustomUpdateResult", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DatabaseHelper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "validator.validator_framework.DatabaseHelper", "name": "DatabaseHelper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "validator.validator_framework.DatabaseHelper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "validator.validator_framework", "mro": ["validator.validator_framework.DatabaseHelper", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "collection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.DatabaseHelper.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "collection"], "arg_types": ["validator.validator_framework.DatabaseHelper", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}], "extra_attrs": null, "type_ref": "pymongo.synchronous.collection.Collection"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DatabaseHelper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_db_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.DatabaseHelper._handle_db_error", "name": "_handle_db_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": ["validator.validator_framework.DatabaseHelper", "builtins.Exception"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_db_error of DatabaseHelper", "ret_type": "validator.validator_framework.ValidationError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aggregate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "pipeline", "error_message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.DatabaseHelper.aggregate", "name": "aggregate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "pipeline", "error_message"], "arg_types": ["validator.validator_framework.DatabaseHelper", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "aggregate of DatabaseHelper", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "collection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "validator.validator_framework.DatabaseHelper.collection", "name": "collection", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}], "extra_attrs": null, "type_ref": "pymongo.synchronous.collection.Collection"}}}, "delete_many": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "query", "error_if_none_deleted"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.DatabaseHelper.delete_many", "name": "delete_many", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "query", "error_if_none_deleted"], "arg_types": ["validator.validator_framework.DatabaseHelper", {".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_many of DatabaseHelper", "ret_type": {".class": "Instance", "args": ["validator.validator_framework.CustomDeleteResult"], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_one": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "query", "error_if_none_deleted"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.DatabaseHelper.delete_one", "name": "delete_one", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "query", "error_if_none_deleted"], "arg_types": ["validator.validator_framework.DatabaseHelper", {".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_one of DatabaseHelper", "ret_type": {".class": "Instance", "args": ["validator.validator_framework.CustomDeleteResult"], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "drop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.DatabaseHelper.drop", "name": "drop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["validator.validator_framework.DatabaseHelper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop of DatabaseHelper", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "query", "projection", "sort", "limit", "skip", "error_message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.DatabaseHelper.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "query", "projection", "sort", "limit", "skip", "error_message"], "arg_types": ["validator.validator_framework.DatabaseHelper", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find of DatabaseHelper", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_one": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "query", "sort", "projection", "error_message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.DatabaseHelper.find_one", "name": "find_one", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "query", "sort", "projection", "error_message"], "arg_types": ["validator.validator_framework.DatabaseHelper", {".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_one of DatabaseHelper", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "insert_one": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.DatabaseHelper.insert_one", "name": "insert_one", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "document"], "arg_types": ["validator.validator_framework.DatabaseHelper", {".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert_one of DatabaseHelper", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_many": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "query", "update", "upsert", "error_if_none_matched"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.DatabaseHelper.update_many", "name": "update_many", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "query", "update", "upsert", "error_if_none_matched"], "arg_types": ["validator.validator_framework.DatabaseHelper", {".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}, {".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_many of DatabaseHelper", "ret_type": {".class": "Instance", "args": ["validator.validator_framework.CustomUpdateResult"], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_one": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "query", "update", "upsert", "error_if_none_matched"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.DatabaseHelper.update_one", "name": "update_one", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "query", "update", "upsert", "error_if_none_matched"], "arg_types": ["validator.validator_framework.DatabaseHelper", {".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}, {".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_one of DatabaseHelper", "ret_type": {".class": "Instance", "args": ["validator.validator_framework.CustomUpdateResult"], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.DatabaseHelper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "validator.validator_framework.DatabaseHelper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Document": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "validator.validator_framework.Document", "line": 58, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "DuplicateKeyError": {".class": "SymbolTableNode", "cross_ref": "pymongo.errors.<PERSON><PERSON><PERSON>ey<PERSON>rror", "kind": "Gdef"}, "F": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "validator.validator_framework.F", "line": 60, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.ApiResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Failure": {".class": "SymbolTableNode", "cross_ref": "returns.result.Failure", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "InvalidDocument": {".class": "SymbolTableNode", "cross_ref": "bson.errors.InvalidDocument", "kind": "Gdef"}, "InvalidId": {".class": "SymbolTableNode", "cross_ref": "bson.errors.InvalidId", "kind": "Gdef"}, "InvalidOperation": {".class": "SymbolTableNode", "cross_ref": "pymongo.errors.InvalidOperation", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MongoClient": {".class": "SymbolTableNode", "cross_ref": "pymongo.synchronous.mongo_client.MongoClient", "kind": "Gdef"}, "NetworkTimeout": {".class": "SymbolTableNode", "cross_ref": "pymongo.errors.NetworkTimeout", "kind": "Gdef"}, "ObjectId": {".class": "SymbolTableNode", "cross_ref": "bson.objectid.ObjectId", "kind": "Gdef"}, "OperationFailure": {".class": "SymbolTableNode", "cross_ref": "pymongo.errors.OperationFailure", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "QueryResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "validator.validator_framework.QueryResult", "name": "QueryResult", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T", "id": 1, "name": "T", "namespace": "validator.validator_framework.QueryResult", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "validator.validator_framework.QueryResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "validator.validator_framework", "mro": ["validator.validator_framework.QueryResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.QueryResult.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T", "id": 1, "name": "T", "namespace": "validator.validator_framework.QueryResult", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T", "id": 1, "name": "T", "namespace": "validator.validator_framework.QueryResult", "upper_bound": "builtins.object", "values": [], "variance": 0}, "validator.validator_framework.ValidationError"], "extra_attrs": null, "type_ref": "returns.result.Result"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QueryResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_to_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.QueryResult._apply_to_value", "name": "_apply_to_value", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "func"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T", "id": 1, "name": "T", "namespace": "validator.validator_framework.QueryResult", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.U", "id": -1, "name": "U", "namespace": "validator.validator_framework.QueryResult._apply_to_value", "upper_bound": "builtins.object", "values": [], "variance": 0}, "validator.validator_framework.ValidationError"], "extra_attrs": null, "type_ref": "returns.result.Result"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_to_value of QueryResult", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.U", "id": -1, "name": "U", "namespace": "validator.validator_framework.QueryResult._apply_to_value", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.U", "id": -1, "name": "U", "namespace": "validator.validator_framework.QueryResult._apply_to_value", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "validator.validator_framework.ValidationError"], "extra_attrs": null, "type_ref": "returns.result.Result"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.U", "id": -1, "name": "U", "namespace": "validator.validator_framework.QueryResult._apply_to_value", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_result": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "validator.validator_framework.QueryResult._result", "name": "_result", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T", "id": 1, "name": "T", "namespace": "validator.validator_framework.QueryResult", "upper_bound": "builtins.object", "values": [], "variance": 0}, "validator.validator_framework.ValidationError"], "extra_attrs": null, "type_ref": "returns.result.Result"}}}, "field": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "field_name", "error_message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.QueryResult.field", "name": "field", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "field_name", "error_message"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T", "id": 1, "name": "T", "namespace": "validator.validator_framework.QueryResult", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field of QueryResult", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "not_empty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "error_message", "status"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.QueryResult.not_empty", "name": "not_empty", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "error_message", "status"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T", "id": 1, "name": "T", "namespace": "validator.validator_framework.QueryResult", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "not_empty of QueryResult", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T", "id": 1, "name": "T", "namespace": "validator.validator_framework.QueryResult", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.QueryResult.result", "name": "result", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T", "id": 1, "name": "T", "namespace": "validator.validator_framework.QueryResult", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "result of QueryResult", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T", "id": 1, "name": "T", "namespace": "validator.validator_framework.QueryResult", "upper_bound": "builtins.object", "values": [], "variance": 0}, "validator.validator_framework.ValidationError"], "extra_attrs": null, "type_ref": "returns.result.Result"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "transformer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.QueryResult.transform", "name": "transform", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "transformer"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T", "id": 1, "name": "T", "namespace": "validator.validator_framework.QueryResult", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transform of QueryResult", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unwrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.QueryResult.unwrap", "name": "unwrap", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T", "id": 1, "name": "T", "namespace": "validator.validator_framework.QueryResult", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unwrap of QueryResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T", "id": 1, "name": "T", "namespace": "validator.validator_framework.QueryResult", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unwrap_or": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.QueryResult.unwrap_or", "name": "unwrap_or", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "default"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T", "id": 1, "name": "T", "namespace": "validator.validator_framework.QueryResult", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unwrap_or of QueryResult", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.QueryResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T", "id": 1, "name": "T", "namespace": "validator.validator_framework.QueryResult", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.QueryResult"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["T"], "typeddict_type": null}}, "RequestValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "validator.validator_framework.RequestValidator", "name": "RequestValidator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "validator.validator_framework.RequestValidator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "validator.validator_framework", "mro": ["validator.validator_framework.RequestValidator", "builtins.object"], "names": {".class": "SymbolTable", "extract_and_validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["data", "fields"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "validator.validator_framework.RequestValidator.extract_and_validate", "name": "extract_and_validate", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["data", "fields"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_and_validate of RequestValidator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "validator.validator_framework.RequestValidator.extract_and_validate", "name": "extract_and_validate", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["data", "fields"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_and_validate of RequestValidator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_json_unwrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "validator.validator_framework.RequestValidator.get_json_unwrap", "name": "get_json_unwrap", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_json_unwrap of RequestValidator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "validator.validator_framework.RequestValidator.get_json_unwrap", "name": "get_json_unwrap", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_json_unwrap of RequestValidator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.RequestValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "validator.validator_framework.RequestValidator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Response": {".class": "SymbolTableNode", "cross_ref": "flask.wrappers.Response", "kind": "Gdef"}, "Result": {".class": "SymbolTableNode", "cross_ref": "returns.result.Result", "kind": "Gdef"}, "ReturnDocument": {".class": "SymbolTableNode", "cross_ref": "pymongo.synchronous.collection.ReturnDocument", "kind": "Gdef"}, "ServerSelectionTimeoutError": {".class": "SymbolTableNode", "cross_ref": "pymongo.errors.ServerSelectionTimeoutError", "kind": "Gdef"}, "Success": {".class": "SymbolTableNode", "cross_ref": "returns.result.Success", "kind": "Gdef"}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "T_val": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "name": "T_val", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "U": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.U", "name": "U", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UnwrapFailedError": {".class": "SymbolTableNode", "cross_ref": "returns.primitives.exceptions.UnwrapFailedError", "kind": "Gdef"}, "ValidationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "validator.validator_framework.ValidationError", "name": "ValidationError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "validator.validator_framework.ValidationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "validator.validator_framework", "mro": ["validator.validator_framework.ValidationError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "status", "message", "code", "data", "status_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.ValidationError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "status", "message", "code", "data", "status_code"], "arg_types": ["validator.validator_framework.ValidationError", "builtins.str", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValidationError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "validator.validator_framework.ValidationError.code", "name": "code", "type": "builtins.str"}}, "data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "validator.validator_framework.ValidationError.data", "name": "data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "validator.validator_framework.ValidationError.message", "name": "message", "type": "builtins.str"}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "validator.validator_framework.ValidationError.status", "name": "status", "type": "builtins.str"}}, "status_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "validator.validator_framework.ValidationError.status_code", "name": "status_code", "type": "builtins.int"}}, "to_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.ValidationError.to_response", "name": "to_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["validator.validator_framework.ValidationError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_response of ValidationError", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.ApiResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.ValidationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "validator.validator_framework.ValidationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "validator.validator_framework.Validator", "name": "Validator", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": 1, "name": "T_val", "namespace": "validator.validator_framework.Validator", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "validator.validator_framework.Validator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "validator.validator_framework", "mro": ["validator.validator_framework.Validator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.Validator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": 1, "name": "T_val", "namespace": "validator.validator_framework.Validator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.Validator"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.ValidatorState"}, "validator.validator_framework.ValidationError"], "extra_attrs": null, "type_ref": "returns.result.Result"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Validator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_result": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "validator.validator_framework.Validator._result", "name": "_result", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.ValidatorState"}, "validator.validator_framework.ValidationError"], "extra_attrs": null, "type_ref": "returns.result.Result"}}}, "is_successful": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.Validator.is_successful", "name": "is_successful", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": 1, "name": "T_val", "namespace": "validator.validator_framework.Validator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.Validator"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_successful of Validator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_valid_object_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.Validator.is_valid_object_id", "name": "is_valid_object_id", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "message"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": 1, "name": "T_val", "namespace": "validator.validator_framework.Validator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.Validator"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_object_id of Validator", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": 1, "name": "T_val", "namespace": "validator.validator_framework.Validator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.Validator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "not_empty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "message", "status"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.Validator.not_empty", "name": "not_empty", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "message", "status"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": 1, "name": "T_val", "namespace": "validator.validator_framework.Validator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.Validator"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "not_empty of Validator", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": 1, "name": "T_val", "namespace": "validator.validator_framework.Validator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.Validator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "of": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["value", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "validator.validator_framework.Validator.of", "name": "of", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["value", "context"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": 1, "name": "T_val", "namespace": "validator.validator_framework.Validator", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "of of Validator", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": 1, "name": "T_val", "namespace": "validator.validator_framework.Validator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.Validator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "validator.validator_framework.Validator.of", "name": "of", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["value", "context"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": 1, "name": "T_val", "namespace": "validator.validator_framework.Validator", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "of of Validator", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": 1, "name": "T_val", "namespace": "validator.validator_framework.Validator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.Validator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "required": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.Validator.required", "name": "required", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "message"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": 1, "name": "T_val", "namespace": "validator.validator_framework.Validator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.Validator"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "required of Validator", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": 1, "name": "T_val", "namespace": "validator.validator_framework.Validator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.Validator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unwrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.Validator.unwrap", "name": "unwrap", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": 1, "name": "T_val", "namespace": "validator.validator_framework.Validator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.Validator"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unwrap of Validator", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": 1, "name": "T_val", "namespace": "validator.validator_framework.Validator", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unwrap_or": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.Validator.unwrap_or", "name": "unwrap_or", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "default"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": 1, "name": "T_val", "namespace": "validator.validator_framework.Validator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.Validator"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unwrap_or of Validator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.Validator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": 1, "name": "T_val", "namespace": "validator.validator_framework.Validator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.Validator"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["T_val"], "typeddict_type": null}}, "ValidatorState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "validator.validator_framework.ValidatorState", "line": 61, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "WriteConcernError": {".class": "SymbolTableNode", "cross_ref": "pymongo.errors.WriteConcernError", "kind": "Gdef"}, "WriteError": {".class": "SymbolTableNode", "cross_ref": "pymongo.errors.WriteError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "validator.validator_framework.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "validator.validator_framework.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "validator.validator_framework.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "validator.validator_framework.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "validator.validator_framework.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "validator.validator_framework.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "get_collection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["client", "db_name", "collection_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.get_collection", "name": "get_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["client", "db_name", "collection_name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.Document"}], "extra_attrs": null, "type_ref": "pymongo.synchronous.mongo_client.MongoClient"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_collection", "ret_type": "validator.validator_framework.DatabaseHelper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_api_exceptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.handle_api_exceptions", "name": "handle_api_exceptions", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["info"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_api_exceptions", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.F"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.F"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_database_exceptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["f"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.handle_database_exceptions", "name": "handle_database_exceptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["f"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.F"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_database_exceptions", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.F"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_successful": {".class": "SymbolTableNode", "cross_ref": "returns.pipeline.is_successful", "kind": "Gdef"}, "jsonify": {".class": "SymbolTableNode", "cross_ref": "flask.json.jsonify", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "validator.validator_framework.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "request": {".class": "SymbolTableNode", "cross_ref": "flask.globals.request", "kind": "Gdef"}, "safe": {".class": "SymbolTableNode", "cross_ref": "returns.result.safe", "kind": "Gdef"}, "validate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["value", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.validate", "name": "validate", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["value", "context"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": -1, "name": "T_val", "namespace": "validator.validator_framework.validate", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": -1, "name": "T_val", "namespace": "validator.validator_framework.validate", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "validator.validator_framework.Validator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "validator.validator_framework.T_val", "id": -1, "name": "T_val", "namespace": "validator.validator_framework.validate", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "validate_request": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["required_fields"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "validator.validator_framework.validate_request", "name": "validate_request", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["required_fields"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_request", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.F"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.F"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "wraps": {".class": "SymbolTableNode", "cross_ref": "functools.wraps", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\validator\\validator_framework.py"}