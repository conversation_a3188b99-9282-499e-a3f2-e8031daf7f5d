{"data_mtime": 1752141448, "dep_lines": [32, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 24, 26, 27, 28, 29, 30, 32, 33, 35, 36, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 31, 25], "dep_prios": [10, 10, 10, 10, 5, 5, 5, 5, 10, 10, 5, 10, 10, 5, 10, 5, 10, 20, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10], "dependencies": ["matplotlib.pyplot", "socket", "select", "struct", "collections", "logging", "enum", "pymongo", "pandas", "os", "typing", "<PERSON><PERSON><PERSON>", "numpy", "tifffile", "time", "threading", "multiprocessing", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "queue", "datetime", "pydantic", "builtins", "_frozen_importlib", "_socket", "_thread", "_typeshed", "abc", "annotated_types", "bson", "bson.codec_options", "pydantic._internal", "pydantic._internal._decorators", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.fields", "pydantic.functional_validators", "pydantic.main", "pydantic.types", "pydantic_core", "pydantic_core.core_schema", "pymongo.common", "pymongo.synchronous", "pymongo.synchronous.collection", "pymongo.synchronous.database", "pymongo.synchronous.mongo_client", "re", "types", "typing_extensions"], "hash": "b9af9a44243abe37e3841f84f3022db15ec93435", "id": "my_code.radar_code", "ignore_all": true, "interface_hash": "25d8a87d9f4da0cac2e60a9a4511a9421489690a", "mtime": 1752141350, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\my_code\\radar_code.py", "plugin_data": null, "size": 97294, "suppressed": ["scipy.interpolate", "snappy"], "version_id": "1.15.0"}