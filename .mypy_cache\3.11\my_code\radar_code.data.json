{".class": "MypyFile", "_fullname": "my_code.radar_code", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ArcSAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "my_code.radar_code.ArcSAR", "name": "ArcSAR", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "my_code.radar_code", "mro": ["my_code.radar_code.ArcSAR", "builtins.object"], "names": {".class": "SymbolTable", "HEADER_FORMAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "my_code.radar_code.ArcSAR.HEADER_FORMAT", "name": "HEADER_FORMAT", "type": "builtins.str"}}, "HEADER_SIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "my_code.radar_code.ArcSAR.HEADER_SIZE", "name": "HEADER_SIZE", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "client_socket", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "client_socket", "address"], "arg_types": ["my_code.radar_code.ArcSAR", "socket.socket", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ArcSAR", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "address": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "my_code.radar_code.ArcSAR.address", "name": "address", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "atmospheric_correction_control": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "extend_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.atmospheric_correction_control", "name": "atmospheric_correction_control", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "extend_code"], "arg_types": ["my_code.radar_code.ArcSAR", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "atmospheric_correction_control of ArcSAR", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "command_queue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "my_code.radar_code.ArcSAR.command_queue", "name": "command_queue", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "queue.Queue"}}}, "connection_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "my_code.radar_code.ArcSAR.connection_time", "name": "connection_time", "type": "builtins.float"}}, "counter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "my_code.radar_code.ArcSAR.counter", "name": "counter", "type": "builtins.int"}}, "download_log_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "log_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.download_log_file", "name": "download_log_file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "log_name"], "arg_types": ["my_code.radar_code.ArcSAR", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "download_log_file of ArcSAR", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format_hex_pretty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["data", "line_length", "indent"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "my_code.radar_code.ArcSAR.format_hex_pretty", "name": "format_hex_pretty", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["data", "line_length", "indent"], "arg_types": ["builtins.bytes", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_hex_pretty of ArcSAR", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "my_code.radar_code.ArcSAR.format_hex_pretty", "name": "format_hex_pretty", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["data", "line_length", "indent"], "arg_types": ["builtins.bytes", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_hex_pretty of ArcSAR", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.get_state", "name": "get_state", "type": null}}, "handle_radar_report": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "header_data", "payload_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.handle_radar_report", "name": "handle_radar_report", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "header_data", "payload_data"], "arg_types": ["my_code.radar_code.ArcSAR", "builtins.bytes", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_radar_report of ArcSAR", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "my_code.radar_code.ArcSAR.id", "name": "id", "type": "builtins.str"}}, "pending_responses": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "my_code.radar_code.ArcSAR.pending_responses", "name": "pending_responses", "type": {".class": "Instance", "args": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["threading.Event", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "polar2cart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["img", "rng_min", "rng_res", "rng_num", "ang_min", "ang_res", "ang_num", "file_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "my_code.radar_code.ArcSAR.polar2cart", "name": "polar2cart", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "my_code.radar_code.ArcSAR.polar2cart", "name": "polar2cart", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["img", "rng_min", "rng_res", "rng_num", "ang_min", "ang_res", "ang_num", "file_path"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "polar2cart of ArcSAR", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "query_log_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.query_log_file", "name": "query_log_file", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["my_code.radar_code.ArcSAR"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query_log_file of ArcSAR", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "query_radar_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.query_radar_state", "name": "query_radar_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["my_code.radar_code.ArcSAR"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query_radar_state of ArcSAR", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "query_scene_parameter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.query_scene_parameter", "name": "query_scene_parameter", "type": null}}, "radar_disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.radar_disconnect", "name": "radar_disconnect", "type": null}}, "radar_work_control": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "extend_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.radar_work_control", "name": "radar_work_control", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "extend_code"], "arg_types": ["my_code.radar_code.ArcSAR", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "radar_work_control of ArcSAR", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recv_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "socket", "default_chunk_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.recv_data", "name": "recv_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "socket", "default_chunk_size"], "arg_types": ["my_code.radar_code.ArcSAR", "socket.socket", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recv_data of ArcSAR", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_or_update_online_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.register_or_update_online_status", "name": "register_or_update_online_status", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["my_code.radar_code.ArcSAR"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_or_update_online_status of ArcSAR", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_scene_parameter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.set_scene_parameter", "name": "set_scene_parameter", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "parameters"], "arg_types": ["my_code.radar_code.ArcSAR", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_scene_parameter of ArcSAR", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "socket": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "my_code.radar_code.ArcSAR.socket", "name": "socket", "type": "socket.socket"}}, "update_radar_imformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "payload_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.update_radar_imformation", "name": "update_radar_imformation", "type": null}}, "update_radar_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.update_radar_time", "name": "update_radar_time", "type": null}}, "upload_defo_or_confi_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "payload_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.upload_defo_or_confi_data", "name": "upload_defo_or_confi_data", "type": null}}, "upload_image_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "payload_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.upload_image_data", "name": "upload_image_data", "type": null}}, "upload_log_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "payload_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.upload_log_file", "name": "upload_log_file", "type": null}}, "upload_moving_target_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "payload_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.ArcSAR.upload_moving_target_data", "name": "upload_moving_target_data", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.ArcSAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "my_code.radar_code.ArcSAR", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BASE_FILE_PATH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "my_code.radar_code.BASE_FILE_PATH", "name": "BASE_FILE_PATH", "type": "builtins.str"}}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CommandHeader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "my_code.radar_code.CommandHeader", "name": "CommandHeader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "my_code.radar_code.CommandHeader", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "my_code.radar_code", "mro": ["my_code.radar_code.CommandHeader", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "PLATFORM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "my_code.radar_code.CommandHeader.PLATFORM", "name": "PLATFORM", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 23130}, "type_ref": "builtins.int"}}}, "RADAR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "my_code.radar_code.CommandHeader.RADAR", "name": "RADAR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 15420}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.CommandHeader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "my_code.radar_code.CommandHeader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "Event": {".class": "SymbolTableNode", "cross_ref": "threading.Event", "kind": "Gdef"}, "Field": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.Field", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "HOST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.HOST", "name": "HOST", "type": "builtins.str"}}, "INFO": {".class": "SymbolTableNode", "cross_ref": "logging.INFO", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "MongoClient": {".class": "SymbolTableNode", "cross_ref": "pymongo.synchronous.mongo_client.MongoClient", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.PORT", "name": "PORT", "type": "builtins.int"}}, "Queue": {".class": "SymbolTableNode", "cross_ref": "queue.Queue", "kind": "Gdef"}, "Radar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "my_code.radar_code.Radar", "name": "Radar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "my_code.radar_code.Radar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["Id_str", "Socket"]}}, "module_name": "my_code.radar_code", "mro": ["my_code.radar_code.Radar", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "Id_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "my_code.radar_code.Radar.Id_str", "name": "Id_str", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "Socket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "my_code.radar_code.Radar.Socket", "name": "Socket", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.Radar._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "my_code.radar_code.Radar.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "my_code.radar_code.Radar.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "my_code.radar_code.Radar.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "Id_str"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "Socket"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "Id_str", "Socket"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "my_code.radar_code.Radar.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "Id_str", "Socket"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.Radar._NT", "id": -1, "name": "_NT", "namespace": "my_code.radar_code.Radar.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of Radar", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.Radar._NT", "id": -1, "name": "_NT", "namespace": "my_code.radar_code.Radar.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.Radar._NT", "id": -1, "name": "_NT", "namespace": "my_code.radar_code.Radar.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.Radar._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.Radar._NT", "id": -1, "name": "_NT", "namespace": "my_code.radar_code.Radar._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of Radar", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.Radar._NT", "id": -1, "name": "_NT", "namespace": "my_code.radar_code.Radar._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "my_code.radar_code.Radar._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "my_code.radar_code.Radar._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "my_code.radar_code.Radar._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "my_code.radar_code.Radar._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.Radar._NT", "id": -1, "name": "_NT", "namespace": "my_code.radar_code.Radar._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of Radar", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.Radar._NT", "id": -1, "name": "_NT", "namespace": "my_code.radar_code.Radar._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.Radar._NT", "id": -1, "name": "_NT", "namespace": "my_code.radar_code.Radar._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "my_code.radar_code.Radar._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.Radar._NT", "id": -1, "name": "_NT", "namespace": "my_code.radar_code.Radar._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of Radar", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.Radar._NT", "id": -1, "name": "_NT", "namespace": "my_code.radar_code.Radar._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.Radar._NT", "id": -1, "name": "_NT", "namespace": "my_code.radar_code.Radar._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "Id_str", "Socket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.Radar._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "Id_str", "Socket"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.Radar._NT", "id": -1, "name": "_NT", "namespace": "my_code.radar_code.Radar._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of Radar", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.Radar._NT", "id": -1, "name": "_NT", "namespace": "my_code.radar_code.Radar._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.Radar._NT", "id": -1, "name": "_NT", "namespace": "my_code.radar_code.Radar._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "my_code.radar_code.Radar._source", "name": "_source", "type": "builtins.str"}}}, "self_type": null, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "RadarManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "my_code.radar_code.RadarManager", "name": "RadarManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "my_code.radar_code.RadarManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "my_code.radar_code", "mro": ["my_code.radar_code.RadarManager", "builtins.object"], "names": {".class": "SymbolTable", "ArcSAR_id_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "my_code.radar_code.RadarManager.ArcSAR_id_map", "name": "ArcSAR_id_map", "type": {".class": "Instance", "args": ["builtins.str", "my_code.radar_code.ArcSAR"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.RadarManager.__init__", "name": "__init__", "type": null}}, "_cache_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "my_code.radar_code.RadarManager._cache_timeout", "name": "_cache_timeout", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "my_code.radar_code.RadarManager._instance", "name": "_instance", "type": {".class": "NoneType"}}}, "_lock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "my_code.radar_code.RadarManager._lock", "name": "_lock", "type": "_thread.LockType"}}, "_map_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "my_code.radar_code.RadarManager._map_lock", "name": "_map_lock", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_recent_radars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "my_code.radar_code.RadarManager._recent_radars", "name": "_recent_radars", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "get_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "my_code.radar_code.RadarManager.get_instance", "name": "get_instance", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "my_code.radar_code.RadarManager.get_instance", "name": "get_instance", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "my_code.radar_code.RadarManager"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_instance of RadarManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_radar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "radar_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.RadarManager.get_radar", "name": "get_radar", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "radar_id"], "arg_types": ["my_code.radar_code.RadarManager", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_radar of RadarManager", "ret_type": {".class": "UnionType", "items": ["my_code.radar_code.ArcSAR", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_radar_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.RadarManager.get_radar_count", "name": "get_radar_count", "type": null}}, "get_radar_ids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.RadarManager.get_radar_ids", "name": "get_radar_ids", "type": null}}, "get_radar_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.RadarManager.get_radar_list", "name": "get_radar_list", "type": null}}, "is_radar_registered": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "radar_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.RadarManager.is_radar_registered", "name": "is_radar_registered", "type": null}}, "register_radar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "radar_id", "radar_instance"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.RadarManager.register_radar", "name": "register_radar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "radar_id", "radar_instance"], "arg_types": ["my_code.radar_code.RadarManager", "builtins.str", "my_code.radar_code.ArcSAR"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_radar of RadarManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unregister_radar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "radar_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.RadarManager.unregister_radar", "name": "unregister_radar", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "radar_id"], "arg_types": ["my_code.radar_code.RadarManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unregister_radar of RadarManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.RadarManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "my_code.radar_code.RadarManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SceneParameter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.main.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "my_code.radar_code.SceneParameter", "name": "SceneParameter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "my_code.radar_code.SceneParameter", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": "RoRate", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 84, "name": "rotation_rate", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 20}, {".class": "LiteralType", "fallback": "builtins.int", "value": 30}, {".class": "LiteralType", "fallback": "builtins.int", "value": 50}, {".class": "LiteralType", "fallback": "builtins.int", "value": 60}], "uses_pep604_syntax": false}}, {"alias": "RotAngBgn", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 88, "name": "rotation_angle_start_degrees", "type": "builtins.float"}, {"alias": "RotAngEnd", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 92, "name": "rotation_angle_end_degrees", "type": "builtins.float"}, {"alias": "RangeMin", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 96, "name": "range_min_meters", "type": "builtins.float"}, {"alias": "RangeMax", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 99, "name": "range_max_meters", "type": "builtins.float"}, {"alias": "RadarMode", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 102, "name": "radar_mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}}, {"alias": "PS_TD", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 106, "name": "point_selection_threshold", "type": "builtins.int"}, {"alias": "StartStopTime", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 116, "name": "start_dwell_time_seconds", "type": "builtins.int"}, {"alias": "EndStopTime", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 119, "name": "end_dwell_time_seconds", "type": "builtins.int"}, {"alias": "ScatImageEn", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 122, "name": "is_scatter_image_enabled", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}}, {"alias": "ScatImageDec", "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 125, "name": "scatter_image_decimation", "type": "builtins.int"}, {"alias": "DefoImageEn", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 131, "name": "is_defo_image_enabled", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}}, {"alias": "DefoImageDec", "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 134, "name": "defo_image_decimation", "type": "builtins.int"}, {"alias": "MissionIDSwitch", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 140, "name": "is_mission_id_enabled", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}}, {"alias": "MissionID", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 144, "name": "mission_id", "type": "builtins.int"}, {"alias": "AntBWAz", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 151, "name": "antenna_azimuth_beam_width_degrees", "type": "builtins.int"}, {"alias": "AntSteerVt", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 154, "name": "antenna_vertical_steering_angle", "type": "builtins.int"}, {"alias": "RadarLon", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 158, "name": "radar_longitude", "type": "builtins.float"}, {"alias": "RadarLat", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 164, "name": "radar_latitude", "type": "builtins.float"}, {"alias": "RadarHei", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 170, "name": "radar_height_meters", "type": "builtins.float"}, {"alias": "RadarOri", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 173, "name": "radar_orientation_degrees", "type": "builtins.float"}, {"alias": "RadarArmLen", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 179, "name": "radar_arm_length_meters", "type": "builtins.float"}, {"alias": "FilterType", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 184, "name": "filter_type", "type": "builtins.float"}, {"alias": "LocaltionType", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 190, "name": "localization_mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "my_code.radar_code", "mro": ["my_code.radar_code.SceneParameter", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "my_code.radar_code.SceneParameter.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 5, 3, 5, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "RoRate", "RotAngBgn", "RotAngEnd", "RangeMin", "RangeMax", "RadarMode", "PS_TD", "StartStopTime", "EndStopTime", "ScatImageEn", "ScatImageDec", "DefoImageEn", "DefoImageDec", "MissionIDSwitch", "MissionID", "AntBWAz", "AntSteerVt", "RadarLon", "RadarLat", "RadarHei", "RadarOri", "RadarArmLen", "FilterType", "LocaltionType"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.SceneParameter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 5, 3, 5, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "RoRate", "RotAngBgn", "RotAngEnd", "RangeMin", "RangeMax", "RadarMode", "PS_TD", "StartStopTime", "EndStopTime", "ScatImageEn", "ScatImageDec", "DefoImageEn", "DefoImageDec", "MissionIDSwitch", "MissionID", "AntBWAz", "AntSteerVt", "RadarLon", "RadarLat", "RadarHei", "RadarOri", "RadarArmLen", "FilterType", "LocaltionType"], "arg_types": ["my_code.radar_code.SceneParameter", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 20}, {".class": "LiteralType", "fallback": "builtins.int", "value": 30}, {".class": "LiteralType", "fallback": "builtins.int", "value": 50}, {".class": "LiteralType", "fallback": "builtins.int", "value": 60}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SceneParameter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "my_code.radar_code.SceneParameter.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rotation_rate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rotation_angle_start_degrees"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rotation_angle_end_degrees"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "range_min_meters"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "range_max_meters"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "radar_mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "point_selection_threshold"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "start_dwell_time_seconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "end_dwell_time_seconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_scatter_image_enabled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "scatter_image_decimation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_defo_image_enabled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "defo_image_decimation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_mission_id_enabled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mission_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "antenna_azimuth_beam_width_degrees"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "antenna_vertical_steering_angle"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "radar_longitude"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "radar_latitude"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "radar_height_meters"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "radar_orientation_degrees"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "radar_arm_length_meters"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "filter_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "localization_mode"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "RoRate", "RotAngBgn", "RotAngEnd", "RangeMin", "RangeMax", "RadarMode", "PS_TD", "StartStopTime", "EndStopTime", "ScatImageEn", "ScatImageDec", "DefoImageEn", "DefoImageDec", "MissionIDSwitch", "MissionID", "AntBWAz", "AntSteerVt", "RadarLon", "RadarLat", "RadarHei", "RadarOri", "RadarArmLen", "FilterType", "LocaltionType"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "my_code.radar_code.SceneParameter.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "RoRate", "RotAngBgn", "RotAngEnd", "RangeMin", "RangeMax", "RadarMode", "PS_TD", "StartStopTime", "EndStopTime", "ScatImageEn", "ScatImageDec", "DefoImageEn", "DefoImageDec", "MissionIDSwitch", "MissionID", "AntBWAz", "AntSteerVt", "RadarLon", "RadarLat", "RadarHei", "RadarOri", "RadarArmLen", "FilterType", "LocaltionType"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 20}, {".class": "LiteralType", "fallback": "builtins.int", "value": 30}, {".class": "LiteralType", "fallback": "builtins.int", "value": 50}, {".class": "LiteralType", "fallback": "builtins.int", "value": 60}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of SceneParameter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "my_code.radar_code.SceneParameter.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "RoRate", "RotAngBgn", "RotAngEnd", "RangeMin", "RangeMax", "RadarMode", "PS_TD", "StartStopTime", "EndStopTime", "ScatImageEn", "ScatImageDec", "DefoImageEn", "DefoImageDec", "MissionIDSwitch", "MissionID", "AntBWAz", "AntSteerVt", "RadarLon", "RadarLat", "RadarHei", "RadarOri", "RadarArmLen", "FilterType", "LocaltionType"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 20}, {".class": "LiteralType", "fallback": "builtins.int", "value": 30}, {".class": "LiteralType", "fallback": "builtins.int", "value": 50}, {".class": "LiteralType", "fallback": "builtins.int", "value": 60}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of SceneParameter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "antenna_azimuth_beam_width_degrees": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.antenna_azimuth_beam_width_degrees", "name": "antenna_azimuth_beam_width_degrees", "type": "builtins.int"}}, "antenna_vertical_steering_angle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.antenna_vertical_steering_angle", "name": "antenna_vertical_steering_angle", "type": "builtins.int"}}, "check_range_min_max": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "my_code.radar_code.SceneParameter.check_range_min_max", "name": "check_range_min_max", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["my_code.radar_code.SceneParameter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_range_min_max of SceneParameter", "ret_type": "my_code.radar_code.SceneParameter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "my_code.radar_code.SceneParameter.check_range_min_max", "name": "check_range_min_max", "type": {".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}}}}, "defo_image_decimation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.defo_image_decimation", "name": "defo_image_decimation", "type": "builtins.int"}}, "end_dwell_time_seconds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.end_dwell_time_seconds", "name": "end_dwell_time_seconds", "type": "builtins.int"}}, "filter_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.filter_type", "name": "filter_type", "type": "builtins.float"}}, "is_defo_image_enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.is_defo_image_enabled", "name": "is_defo_image_enabled", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}}}, "is_mission_id_enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.is_mission_id_enabled", "name": "is_mission_id_enabled", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}}}, "is_scatter_image_enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.is_scatter_image_enabled", "name": "is_scatter_image_enabled", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}}}, "localization_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.localization_mode", "name": "localization_mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}}}, "mission_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.mission_id", "name": "mission_id", "type": "builtins.int"}}, "model_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.model_config", "name": "model_config", "type": {".class": "TypedDictType", "fallback": "pydantic.config.ConfigDict", "items": [["title", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["model_title_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.type"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["field_title_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["pydantic.fields.FieldInfo", "pydantic.fields.ComputedFieldInfo"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["str_to_lower", "builtins.bool"], ["str_to_upper", "builtins.bool"], ["str_strip_whitespace", "builtins.bool"], ["str_min_length", "builtins.int"], ["str_max_length", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["extra", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.ExtraValues"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["frozen", "builtins.bool"], ["populate_by_name", "builtins.bool"], ["use_enum_values", "builtins.bool"], ["validate_assignment", "builtins.bool"], ["arbitrary_types_allowed", "builtins.bool"], ["from_attributes", "builtins.bool"], ["loc_by_alias", "builtins.bool"], ["alias_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "pydantic.aliases.AliasGenerator", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["ignored_types", {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["allow_inf_nan", "builtins.bool"], ["json_schema_extra", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonDict"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonSchemaExtraCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["json_encoders", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.object"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonEncoder"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["strict", "builtins.bool"], ["revalidate_instances", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "always"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "never"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "subclass-instances"}], "uses_pep604_syntax": false}], ["ser_j<PERSON>_<PERSON><PERSON><PERSON>", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "iso8601"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float"}], "uses_pep604_syntax": false}], ["ser_json_bytes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "utf8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "base64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hex"}], "uses_pep604_syntax": false}], ["val_json_bytes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "utf8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "base64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hex"}], "uses_pep604_syntax": false}], ["ser_json_inf_nan", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "null"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "constants"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strings"}], "uses_pep604_syntax": false}], ["validate_default", "builtins.bool"], ["validate_return", "builtins.bool"], ["protected_namespaces", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["hide_input_in_errors", "builtins.bool"], ["defer_build", "builtins.bool"], ["plugin_settings", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["schema_generator", {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic._internal._generate_schema.GenerateSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["json_schema_serialization_defaults_required", "builtins.bool"], ["json_schema_mode_override", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["coerce_numbers_to_str", "builtins.bool"], ["regex_engine", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rust-regex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "python-re"}], "uses_pep604_syntax": false}], ["validation_error_cause", "builtins.bool"], ["use_attribute_docstrings", "builtins.bool"], ["cache_strings", {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "all"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keys"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}], "uses_pep604_syntax": true}], ["validate_by_alias", "builtins.bool"], ["validate_by_name", "builtins.bool"], ["serialize_by_alias", "builtins.bool"]], "readonly_keys": [], "required_keys": []}}}, "pack_to_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.SceneParameter.pack_to_bytes", "name": "pack_to_bytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["my_code.radar_code.SceneParameter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pack_to_bytes of SceneParameter", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "point_selection_threshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.point_selection_threshold", "name": "point_selection_threshold", "type": "builtins.int"}}, "radar_arm_length_meters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.radar_arm_length_meters", "name": "radar_arm_length_meters", "type": "builtins.float"}}, "radar_height_meters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.radar_height_meters", "name": "radar_height_meters", "type": "builtins.float"}}, "radar_latitude": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.radar_latitude", "name": "radar_latitude", "type": "builtins.float"}}, "radar_longitude": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.radar_longitude", "name": "radar_longitude", "type": "builtins.float"}}, "radar_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.radar_mode", "name": "radar_mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}}}, "radar_orientation_degrees": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.radar_orientation_degrees", "name": "radar_orientation_degrees", "type": "builtins.float"}}, "range_max_meters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.range_max_meters", "name": "range_max_meters", "type": "builtins.float"}}, "range_min_meters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.range_min_meters", "name": "range_min_meters", "type": "builtins.float"}}, "rotation_angle_end_degrees": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.rotation_angle_end_degrees", "name": "rotation_angle_end_degrees", "type": "builtins.float"}}, "rotation_angle_start_degrees": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.rotation_angle_start_degrees", "name": "rotation_angle_start_degrees", "type": "builtins.float"}}, "rotation_rate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.rotation_rate", "name": "rotation_rate", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 20}, {".class": "LiteralType", "fallback": "builtins.int", "value": 30}, {".class": "LiteralType", "fallback": "builtins.int", "value": 50}, {".class": "LiteralType", "fallback": "builtins.int", "value": 60}], "uses_pep604_syntax": false}}}, "scatter_image_decimation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.scatter_image_decimation", "name": "scatter_image_decimation", "type": "builtins.int"}}, "start_dwell_time_seconds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.SceneParameter.start_dwell_time_seconds", "name": "start_dwell_time_seconds", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "my_code.radar_code.SceneParameter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "my_code.radar_code.SceneParameter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "pydantic.ValidationError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.radar_code.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.radar_code.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.radar_code.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.radar_code.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.radar_code.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.radar_code.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "basicConfig": {".class": "SymbolTableNode", "cross_ref": "logging.basicConfig", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "client": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "my_code.radar_code.client", "name": "client", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "pymongo.synchronous.mongo_client.MongoClient"}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "db_base_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "my_code.radar_code.db_base_data", "name": "db_base_data", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "pymongo.synchronous.database.Database"}}}, "getLogger": {".class": "SymbolTableNode", "cross_ref": "logging.getLogger", "kind": "Gdef"}, "griddata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "my_code.radar_code.griddata", "name": "griddata", "type": {".class": "AnyType", "missing_import_name": "my_code.radar_code.griddata", "source_any": null, "type_of_any": 3}}}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "imread": {".class": "SymbolTableNode", "cross_ref": "tifffile.tifffile.imread", "kind": "Gdef"}, "imwrite": {".class": "SymbolTableNode", "cross_ref": "tifffile.tifffile.imwrite", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "my_code.radar_code.logger", "name": "logger", "type": "logging.Logger"}}, "manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "my_code.radar_code.manager", "name": "manager", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "model_validator": {".class": "SymbolTableNode", "cross_ref": "pydantic.functional_validators.model_validator", "kind": "Gdef"}, "mp": {".class": "SymbolTableNode", "cross_ref": "multiprocessing", "kind": "Gdef"}, "namedtuple": {".class": "SymbolTableNode", "cross_ref": "collections.namedtuple", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "pd": {".class": "SymbolTableNode", "cross_ref": "pandas", "kind": "Gdef"}, "plt": {".class": "SymbolTableNode", "cross_ref": "matplotlib.pyplot", "kind": "Gdef"}, "radar_collection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "my_code.radar_code.radar_collection", "name": "radar_collection", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "pymongo.synchronous.collection.Collection"}}}, "run_all_tests": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.run_all_tests", "name": "run_all_tests", "type": null}}, "select": {".class": "SymbolTableNode", "cross_ref": "select", "kind": "Gdef"}, "simulate_radar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["host", "port"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.simulate_radar", "name": "simulate_radar", "type": null}}, "snappy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "my_code.radar_code.snappy", "name": "snappy", "type": {".class": "AnyType", "missing_import_name": "my_code.radar_code.snappy", "source_any": null, "type_of_any": 3}}}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "struct": {".class": "SymbolTableNode", "cross_ref": "struct", "kind": "Gdef"}, "tcp_server": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.tcp_server", "name": "tcp_server", "type": null}}, "test_atmospheric_correction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["radar_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.test_atmospheric_correction", "name": "test_atmospheric_correction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["radar_client"], "arg_types": ["my_code.radar_code.ArcSAR"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test_atmospheric_correction", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_log_management": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["radar_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.test_log_management", "name": "test_log_management", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["radar_client"], "arg_types": ["my_code.radar_code.ArcSAR"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test_log_management", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_query_radar_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["radar_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.test_query_radar_state", "name": "test_query_radar_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["radar_client"], "arg_types": ["my_code.radar_code.ArcSAR"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test_query_radar_state", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_scene_parameter_management": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["radar_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.test_scene_parameter_management", "name": "test_scene_parameter_management", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["radar_client"], "arg_types": ["my_code.radar_code.ArcSAR"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test_scene_parameter_management", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_work_control": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["radar_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "my_code.radar_code.test_work_control", "name": "test_work_control", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["radar_client"], "arg_types": ["my_code.radar_code.ArcSAR"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test_work_control", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\my_code\\radar_code.py"}