2025-07-09 22:41:49,856 - INFO - 注册蓝图
2025-07-09 22:41:49,861 - INFO - 配置数据库并连接
2025-07-09 22:41:49,861 - INFO - 正在创建并初始化MongoDB单例客户端，URI: 'mongodb://localhost:27017/base_data'
2025-07-09 22:41:49,865 - INFO - MongoDB单例客户端已成功连接并验证。
2025-07-09 22:41:49,865 - INFO - 数据库连接成功
2025-07-09 22:41:49,865 - INFO - 获取数据库连接
2025-07-09 22:41:49,865 - INFO - 共享配置初始化完成
2025-07-09 22:41:49,875 - WARNING -  * Debugger is active!
2025-07-09 22:41:49,881 - INFO -  * Debugger PIN: 875-839-541
2025-07-09 22:41:50,678 - INFO - 服务器线程已启动。
2025-07-09 22:41:50,678 - INFO - [雷达服务器] 雷达服务器已启动，监听端口127.0.0.1:1030
2025-07-09 22:41:51,680 - INFO - 雷达模拟器线程已启动。
2025-07-09 22:41:51,681 - INFO - [模拟器] 已连接到服务器 127.0.0.1:1030
2025-07-09 22:41:51,681 - INFO - [模拟器] 发送初始化注册包...
2025-07-09 22:41:51,681 - INFO - [雷达服务器] 来自('127.0.0.1', 58556)的新连接
2025-07-09 22:41:51,681 - INFO - [雷达(未注册)] 正从('127.0.0.1', 58556)处接受头部数据，目标长度为28
2025-07-09 22:41:51,681 - INFO - [雷达(未注册)] 成功在('127.0.0.1', 58556)接收到头部数据，长度为28
2025-07-09 22:41:51,681 - INFO - [雷达(未注册)] 头部数据为
  00000000:  3C 3C AD 05 10 00 40 06 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-09 22:41:51,681 - INFO - [雷达(未注册)] 从('127.0.0.1', 58556)解析得到的数据长度为0
2025-07-09 22:41:51,681 - INFO - [雷达(未注册)] 开始从('127.0.0.1', 58556)处接受数据，目标长度为0
2025-07-09 22:41:51,681 - INFO - [雷达(未注册)] 成功在('127.0.0.1', 58556)接收到主要数据，长度为0
2025-07-09 22:41:51,681 - INFO - [雷达(未注册)] 主要数据为空
2025-07-09 22:41:51,681 - INFO - [雷达001005AD] 来自('127.0.0.1', 58556)的雷达的设备ID为: 001005AD
2025-07-09 22:41:51,705 - INFO - [雷达001005AD] 已经在数据库中注册
2025-07-09 22:41:51,706 - INFO - [雷达001005AD] 重新上线，更新在线状态为1
2025-07-09 22:41:51,706 - INFO - [雷达001005AD] 客户端发送了数据，命令码=0x40, 扩展码=0x06, 主要数据长度=0
2025-07-09 22:41:51,706 - INFO - [雷达001005AD] 服务器分配处理函数为update_radar_time
2025-07-09 22:41:51,706 - INFO - [雷达001005AD] 成功更新雷达的时间戳为1752072111, 对应时间为2025-07-09 22:41:51
2025-07-09 22:41:51,706 - INFO - [雷达001005AD] 服务器成功发送响应数据，长度为4，内容为AF 7F 6E 68
2025-07-09 22:41:51,706 - INFO - [模拟器] 收到服务器对注册包的响应，完成注册和授时。授时结果为2025-07-09 22:41:51
2025-07-09 22:41:53,681 - INFO - === STARTING TEST SUITE FOR RADAR [001005AD] ===
2025-07-09 22:41:53,681 - INFO - --- [TEST] RUNNING: Query Radar State ---
2025-07-09 22:41:53,681 - INFO - [雷达001005AD] 开始查询雷达的状态
2025-07-09 22:41:53,681 - INFO - [雷达001005AD] 已将counter=1的状态查询指令放入队列
2025-07-09 22:41:53,718 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-07-09 22:41:53,718 - INFO - [模拟器] 收到指令: cmd=0x02, ext=0x00, counter=1
2025-07-09 22:41:53,718 - INFO - [模拟器] 已为 counter 1 发送响应 (载荷长度: 0)
2025-07-09 22:41:53,718 - INFO - [雷达001005AD] 正从('127.0.0.1', 58556)处接受头部数据，目标长度为28
2025-07-09 22:41:53,718 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到头部数据，长度为28
2025-07-09 22:41:53,718 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 02 00 01 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-09 22:41:53,718 - INFO - [雷达001005AD] 从('127.0.0.1', 58556)解析得到的数据长度为0
2025-07-09 22:41:53,718 - INFO - [雷达001005AD] 开始从('127.0.0.1', 58556)处接受数据，目标长度为0
2025-07-09 22:41:53,718 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到主要数据，长度为0
2025-07-09 22:41:53,718 - INFO - [雷达001005AD] 主要数据为空
2025-07-09 22:41:53,718 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=1
2025-07-09 22:41:53,718 - INFO - [雷达001005AD] 雷达状态查询成功，状态为设置正确
2025-07-09 22:41:53,718 - INFO - [雷达001005AD] 开始解析雷达的基本信息
2025-07-09 22:41:53,718 - INFO - [雷达001005AD] 开始更新雷达数据库中的基本信息
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第2位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第6位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第10位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第14位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第18位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第22位开始发生位置: unpack requires a buffer of 2 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第30位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第34位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第38位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第42位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第46位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第50位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第54位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第58位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第62位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第66位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第70位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第74位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第78位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第82位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第86位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第90位开始发生位置: unpack requires a buffer of 8 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第98位开始发生位置: unpack requires a buffer of 8 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第106位开始发生位置: unpack requires a buffer of 8 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第114位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第118位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,720 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第122位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第126位开始发生位置: unpack requires a buffer of 1 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第127位开始发生位置: unpack requires a buffer of 1 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第128位开始发生位置: unpack requires a buffer of 1 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第129位开始发生位置: unpack requires a buffer of 1 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第130位开始发生位置: unpack requires a buffer of 1 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第131位开始发生位置: unpack requires a buffer of 1 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第132位开始发生位置: unpack requires a buffer of 1 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第133位开始发生位置: unpack requires a buffer of 1 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第134位开始发生位置: unpack requires a buffer of 1 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第135位开始发生位置: unpack requires a buffer of 1 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第136位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第140位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第144位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第148位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第152位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第156位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第168位开始发生位置: unpack requires a buffer of 1 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第169位开始发生位置: unpack requires a buffer of 1 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第170位开始发生位置: unpack requires a buffer of 1 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第171位开始发生位置: unpack requires a buffer of 1 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第172位开始发生位置: unpack requires a buffer of 1 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第173位开始发生位置: unpack requires a buffer of 1 bytes
2025-07-09 22:41:53,721 - ERROR - [雷达001005AD] 数据解析错误，从负载数据的第174位开始发生位置: unpack requires a buffer of 4 bytes
2025-07-09 22:41:53,721 - INFO - [雷达001005AD] 成功更新雷达001005AD数据库中的基本信息
2025-07-09 22:41:53,721 - INFO - 状态查询结果: success
2025-07-09 22:41:53,721 - INFO - --- [TEST] PASSED: Query Radar State ---
2025-07-09 22:41:54,723 - INFO - --- [TEST] RUNNING: Work Control ---
2025-07-09 22:41:54,723 - INFO - Testing: Start Work (0x00)
2025-07-09 22:41:54,723 - INFO - [雷达001005AD] 开始向雷达发送工作控制指令:开始工作
2025-07-09 22:41:54,723 - INFO - [雷达001005AD] 已将counter=2的工作控制指令放入队列:开始工作
2025-07-09 22:41:54,727 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-07-09 22:41:54,727 - INFO - [模拟器] 收到指令: cmd=0x03, ext=0x00, counter=2
2025-07-09 22:41:54,727 - INFO - [模拟器] 已为 counter 2 发送响应 (载荷长度: 0)
2025-07-09 22:41:54,727 - INFO - [雷达001005AD] 正从('127.0.0.1', 58556)处接受头部数据，目标长度为28
2025-07-09 22:41:54,727 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到头部数据，长度为28
2025-07-09 22:41:54,727 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 03 00 01 02 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-09 22:41:54,728 - INFO - [雷达001005AD] 从('127.0.0.1', 58556)解析得到的数据长度为0
2025-07-09 22:41:54,728 - INFO - [雷达001005AD] 开始从('127.0.0.1', 58556)处接受数据，目标长度为0
2025-07-09 22:41:54,728 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到主要数据，长度为0
2025-07-09 22:41:54,728 - INFO - [雷达001005AD] 主要数据为空
2025-07-09 22:41:54,728 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=2
2025-07-09 22:41:54,731 - INFO - [雷达001005AD] 雷达开始工作
2025-07-09 22:41:54,731 - INFO - 开始工作结果: success
2025-07-09 22:41:55,232 - INFO - Testing: Stop Work (0x01)
2025-07-09 22:41:55,232 - INFO - [雷达001005AD] 开始向雷达发送工作控制指令:停止工作
2025-07-09 22:41:55,232 - INFO - [雷达001005AD] 已将counter=3的工作控制指令放入队列:停止工作
2025-07-09 22:41:55,735 - INFO - [模拟器] 收到指令: cmd=0x03, ext=0x01, counter=3
2025-07-09 22:41:55,735 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-07-09 22:41:55,735 - INFO - [模拟器] 已为 counter 3 发送响应 (载荷长度: 0)
2025-07-09 22:41:55,735 - INFO - [雷达001005AD] 正从('127.0.0.1', 58556)处接受头部数据，目标长度为28
2025-07-09 22:41:55,735 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到头部数据，长度为28
2025-07-09 22:41:55,735 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 03 01 01 03 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-09 22:41:55,735 - INFO - [雷达001005AD] 从('127.0.0.1', 58556)解析得到的数据长度为0
2025-07-09 22:41:55,735 - INFO - [雷达001005AD] 开始从('127.0.0.1', 58556)处接受数据，目标长度为0
2025-07-09 22:41:55,735 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到主要数据，长度为0
2025-07-09 22:41:55,735 - INFO - [雷达001005AD] 主要数据为空
2025-07-09 22:41:55,735 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=3
2025-07-09 22:41:55,737 - INFO - [雷达001005AD] 雷达停止工作
2025-07-09 22:41:55,737 - INFO - 停止工作结果: success
2025-07-09 22:41:56,238 - INFO - Testing: Reboot (0x02)
2025-07-09 22:41:56,238 - INFO - [雷达001005AD] 开始向雷达发送工作控制指令:重启雷达
2025-07-09 22:41:56,238 - INFO - [雷达001005AD] 已将counter=4的工作控制指令放入队列:重启雷达
2025-07-09 22:41:56,744 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-07-09 22:41:56,744 - INFO - [模拟器] 收到指令: cmd=0x03, ext=0x02, counter=4
2025-07-09 22:41:56,744 - INFO - [模拟器] 已为 counter 4 发送响应 (载荷长度: 0)
2025-07-09 22:41:56,744 - INFO - [雷达001005AD] 正从('127.0.0.1', 58556)处接受头部数据，目标长度为28
2025-07-09 22:41:56,745 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到头部数据，长度为28
2025-07-09 22:41:56,745 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 03 02 01 04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-09 22:41:56,745 - INFO - [雷达001005AD] 从('127.0.0.1', 58556)解析得到的数据长度为0
2025-07-09 22:41:56,745 - INFO - [雷达001005AD] 开始从('127.0.0.1', 58556)处接受数据，目标长度为0
2025-07-09 22:41:56,745 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到主要数据，长度为0
2025-07-09 22:41:56,745 - INFO - [雷达001005AD] 主要数据为空
2025-07-09 22:41:56,745 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=4
2025-07-09 22:41:56,747 - INFO - [雷达001005AD] 雷达重启
2025-07-09 22:41:56,747 - INFO - 重启雷达结果: success
2025-07-09 22:41:56,748 - INFO - --- [TEST] PASSED: Work Control ---
2025-07-09 22:41:57,749 - INFO - --- [TEST] RUNNING: Scene Parameter Management ---
2025-07-09 22:41:57,749 - INFO - Testing: Set Valid Scene Parameters
2025-07-09 22:41:57,749 - INFO - [雷达001005AD] 开始设置雷达的场景参数
2025-07-09 22:41:57,749 - INFO - [雷达001005AD] 雷达场景参数校验成功
2025-07-09 22:41:57,749 - INFO - [雷达001005AD] 已将counter=5的场景参数设置指令放入队列
2025-07-09 22:41:57,751 - INFO - [模拟器] 收到指令: cmd=0x00, ext=0x00, counter=5
2025-07-09 22:41:57,751 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-07-09 22:41:57,751 - INFO - [雷达001005AD] 正从('127.0.0.1', 58556)处接受头部数据，目标长度为28
2025-07-09 22:41:57,751 - INFO - [模拟器] 已为 counter 5 发送响应 (载荷长度: 0)
2025-07-09 22:41:57,751 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到头部数据，长度为28
2025-07-09 22:41:57,751 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 00 00 01 05 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-09 22:41:57,751 - INFO - [雷达001005AD] 从('127.0.0.1', 58556)解析得到的数据长度为0
2025-07-09 22:41:57,751 - INFO - [雷达001005AD] 开始从('127.0.0.1', 58556)处接受数据，目标长度为0
2025-07-09 22:41:57,752 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到主要数据，长度为0
2025-07-09 22:41:57,752 - INFO - [雷达001005AD] 主要数据为空
2025-07-09 22:41:57,752 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=5
2025-07-09 22:41:57,752 - INFO - [雷达001005AD] 雷达场景参数设置成功，状态为设置正确
2025-07-09 22:41:57,752 - INFO - 设置有效参数结果: success
2025-07-09 22:41:58,253 - INFO - Testing: Query Scene Parameters
2025-07-09 22:41:58,253 - INFO - [雷达001005AD] 开始查询雷达的场景参数
2025-07-09 22:41:58,253 - INFO - [雷达001005AD] 已将counter=6的场景参数查询指令放入队列
2025-07-09 22:41:58,762 - INFO - [模拟器] 收到指令: cmd=0x01, ext=0x00, counter=6
2025-07-09 22:41:58,762 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-07-09 22:41:58,762 - INFO - [模拟器] 构建【查询场景参数】的响应载荷...
2025-07-09 22:41:58,762 - INFO - [雷达001005AD] 正从('127.0.0.1', 58556)处接受头部数据，目标长度为28
2025-07-09 22:41:58,762 - INFO - [模拟器] 已为 counter 6 发送响应 (载荷长度: 106)
2025-07-09 22:41:58,762 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到头部数据，长度为28
2025-07-09 22:41:58,762 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 01 00 01 06 00 00 00 00 00 00 00 00 00 00 00 00 00 00 6A 00 00 00
2025-07-09 22:41:58,762 - INFO - [雷达001005AD] 从('127.0.0.1', 58556)解析得到的数据长度为106
2025-07-09 22:41:58,763 - INFO - [雷达001005AD] 开始从('127.0.0.1', 58556)处接受数据，目标长度为106
2025-07-09 22:41:58,763 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到主要数据，长度为106
2025-07-09 22:41:58,763 - INFO - [雷达001005AD] 主要数据为
  00000000:  1E 00 00 00 00 00 00 80 34 43 00 00 20 41 00 40 9C 45 00 00 00 00 14 00 00 00 00 00
  0000001c:  00 00 00 00 00 00 00 00 00 00 05 00 05 00 01 00 02 00 01 00 01 00 01 00 39 30 00 00
  00000038:  00 00 00 00 02 00 05 00 29 5C 8F C2 F5 18 5D 40 14 AE 47 E1 7A F4 43 40 00 00 00 00
  00000054:  00 00 49 40 00 00 B4 42 00 00 00 3F 00 00 40 40 00 00 00 00 00 00                  
2025-07-09 22:41:58,763 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=6
2025-07-09 22:41:58,769 - INFO - [雷达001005AD] 设备场景参数查询成功
2025-07-09 22:41:58,769 - INFO - 查询参数结果: success
2025-07-09 22:41:59,270 - INFO - Testing: Set Invalid Scene Parameters (should fail validation)
2025-07-09 22:41:59,270 - INFO - [雷达001005AD] 开始设置雷达的场景参数
2025-07-09 22:41:59,270 - ERROR - [雷达001005AD] 雷达场景参数校验失败: 20 validation errors for SceneParameter
RoRate
  Input should be 0, 20, 30, 50 or 60 [type=literal_error, input_value=99, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/literal_error
RotAngBgn
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RotAngEnd
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarMode
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
PS_TD
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
StartStopTime
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
EndStopTime
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
ScatImageEn
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
DefoImageEn
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
MissionIDSwitch
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
MissionID
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
AntBWAz
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
AntSteerVt
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarLon
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarLat
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarHei
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarOri
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarArmLen
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
FilterType
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
LocaltionType
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-07-09 22:41:59,270 - INFO - 设置无效参数结果: [雷达001005AD] 雷达场景参数校验失败: 20 validation errors for SceneParameter
RoRate
  Input should be 0, 20, 30, 50 or 60 [type=literal_error, input_value=99, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/literal_error
RotAngBgn
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RotAngEnd
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarMode
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
PS_TD
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
StartStopTime
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
EndStopTime
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
ScatImageEn
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
DefoImageEn
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
MissionIDSwitch
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
MissionID
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
AntBWAz
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
AntSteerVt
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarLon
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarLat
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarHei
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarOri
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarArmLen
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
FilterType
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
LocaltionType
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-07-09 22:41:59,270 - INFO - --- [TEST] PASSED: Scene Parameter Management ---
2025-07-09 22:42:00,271 - INFO - --- [TEST] RUNNING: Atmospheric Correction Control ---
2025-07-09 22:42:00,271 - INFO - Testing: Turn On (0x01)
2025-07-09 22:42:00,271 - INFO - [雷达001005AD] 开始向雷达发送大气校正控制指令:大气校正开
2025-07-09 22:42:00,271 - INFO - [雷达001005AD] 已将counter=8的大气校正控制指令放入队列:大气校正开
2025-07-09 22:42:00,782 - INFO - [模拟器] 收到指令: cmd=0x05, ext=0x01, counter=8
2025-07-09 22:42:00,782 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-07-09 22:42:00,782 - INFO - [雷达001005AD] 正从('127.0.0.1', 58556)处接受头部数据，目标长度为28
2025-07-09 22:42:00,782 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到头部数据，长度为28
2025-07-09 22:42:00,782 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 05 01 01 08 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-09 22:42:00,782 - INFO - [雷达001005AD] 从('127.0.0.1', 58556)解析得到的数据长度为0
2025-07-09 22:42:00,782 - INFO - [模拟器] 已为 counter 8 发送响应 (载荷长度: 0)
2025-07-09 22:42:00,782 - INFO - [雷达001005AD] 开始从('127.0.0.1', 58556)处接受数据，目标长度为0
2025-07-09 22:42:00,782 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到主要数据，长度为0
2025-07-09 22:42:00,782 - INFO - [雷达001005AD] 主要数据为空
2025-07-09 22:42:00,783 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=8
2025-07-09 22:42:00,783 - INFO - [雷达001005AD] 大气校正开控制成功
2025-07-09 22:42:00,783 - INFO - 开启大气校正结果: success
2025-07-09 22:42:01,283 - INFO - Testing: Turn Off (0x00)
2025-07-09 22:42:01,283 - INFO - [雷达001005AD] 开始向雷达发送大气校正控制指令:大气校正关
2025-07-09 22:42:01,283 - INFO - [雷达001005AD] 已将counter=9的大气校正控制指令放入队列:大气校正关
2025-07-09 22:42:01,796 - INFO - [模拟器] 收到指令: cmd=0x05, ext=0x00, counter=9
2025-07-09 22:42:01,796 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-07-09 22:42:01,796 - INFO - [雷达001005AD] 正从('127.0.0.1', 58556)处接受头部数据，目标长度为28
2025-07-09 22:42:01,796 - INFO - [模拟器] 已为 counter 9 发送响应 (载荷长度: 0)
2025-07-09 22:42:01,796 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到头部数据，长度为28
2025-07-09 22:42:01,796 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 05 00 01 09 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-09 22:42:01,796 - INFO - [雷达001005AD] 从('127.0.0.1', 58556)解析得到的数据长度为0
2025-07-09 22:42:01,796 - INFO - [雷达001005AD] 开始从('127.0.0.1', 58556)处接受数据，目标长度为0
2025-07-09 22:42:01,797 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到主要数据，长度为0
2025-07-09 22:42:01,797 - INFO - [雷达001005AD] 主要数据为空
2025-07-09 22:42:01,797 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=9
2025-07-09 22:42:01,797 - INFO - [雷达001005AD] 大气校正关控制成功
2025-07-09 22:42:01,797 - INFO - 关闭大气校正结果: success
2025-07-09 22:42:01,797 - INFO - --- [TEST] PASSED: Atmospheric Correction Control ---
2025-07-09 22:42:02,797 - INFO - --- [TEST] RUNNING: Log File Management ---
2025-07-09 22:42:02,797 - INFO - Testing: Query Log File List
2025-07-09 22:42:02,797 - INFO - [雷达001005AD] 开始查询雷达的日志
2025-07-09 22:42:02,797 - INFO - [雷达001005AD] 已将counter=10的日志查询指令放入队列
2025-07-09 22:42:02,801 - INFO - [模拟器] 收到指令: cmd=0x08, ext=0x00, counter=10
2025-07-09 22:42:02,801 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-07-09 22:42:02,801 - INFO - [模拟器] 构建【查询日志列表】的响应载荷...
2025-07-09 22:42:02,802 - INFO - [雷达001005AD] 正从('127.0.0.1', 58556)处接受头部数据，目标长度为28
2025-07-09 22:42:02,802 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到头部数据，长度为28
2025-07-09 22:42:02,802 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 08 00 01 0A 00 00 00 00 00 00 00 00 00 00 00 00 00 00 31 00 00 00
2025-07-09 22:42:02,802 - INFO - [雷达001005AD] 从('127.0.0.1', 58556)解析得到的数据长度为49
2025-07-09 22:42:02,802 - INFO - [雷达001005AD] 开始从('127.0.0.1', 58556)处接受数据，目标长度为49
2025-07-09 22:42:02,802 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到主要数据，长度为49
2025-07-09 22:42:02,802 - INFO - [雷达001005AD] 主要数据为
  00000000:  72 61 64 61 72 5F 32 30 32 34 2D 30 36 2D 31 38 2E 6C 6F 67 2C 73 79 73 74 65 6D 5F
  0000001c:  65 76 65 6E 74 73 2E 6C 6F 67 2C 63 6F 6E 66 69 67 2E 74 78 74                     
2025-07-09 22:42:02,802 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=10
2025-07-09 22:42:02,802 - INFO - [模拟器] 已为 counter 10 发送响应 (载荷长度: 49)
2025-07-09 22:42:02,802 - INFO - [雷达001005AD] 雷达日志查询成功
2025-07-09 22:42:02,803 - INFO - [雷达001005AD] 日志radar_2024-06-18.log已存在
2025-07-09 22:42:02,804 - INFO - [雷达001005AD] 日志system_events.log已存在
2025-07-09 22:42:02,805 - INFO - [雷达001005AD] 日志config.txt已存在
2025-07-09 22:42:02,805 - INFO - [雷达001005AD] 雷达日志查询成功
2025-07-09 22:42:02,805 - INFO - 查询日志列表结果: success
2025-07-09 22:42:03,307 - INFO - Testing: Download Log File 'radar_2024-06-18.log'
2025-07-09 22:42:03,307 - INFO - [雷达001005AD] 开始下载雷达日志radar_2024-06-18.log
2025-07-09 22:42:03,307 - INFO - [雷达001005AD] 已将counter=11的日志下载指令放入队列
2025-07-09 22:42:03,811 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-07-09 22:42:03,811 - INFO - [模拟器] 收到指令: cmd=0x08, ext=0x01, counter=11
2025-07-09 22:42:03,811 - INFO - [模拟器] 构建 [下载日志文件: radar_2024-06-18.log]的响应载荷...
2025-07-09 22:42:03,811 - INFO - [模拟器] 已为 counter 11 发送响应 (载荷长度: 226)
2025-07-09 22:42:03,811 - INFO - [雷达001005AD] 正从('127.0.0.1', 58556)处接受头部数据，目标长度为28
2025-07-09 22:42:03,811 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到头部数据，长度为28
2025-07-09 22:42:03,811 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 08 01 01 0B 00 00 00 00 00 00 00 00 00 00 00 00 00 00 E2 00 00 00
2025-07-09 22:42:03,811 - INFO - [雷达001005AD] 从('127.0.0.1', 58556)解析得到的数据长度为226
2025-07-09 22:42:03,811 - INFO - [雷达001005AD] 开始从('127.0.0.1', 58556)处接受数据，目标长度为226
2025-07-09 22:42:03,811 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到主要数据，长度为226
2025-07-09 22:42:03,811 - INFO - [雷达001005AD] 主要数据为
  00000000:  72 61 64 61 72 5F 32 30 32 34 2D 30 36 2D 31 38 2E 6C 6F 67 00 00 00 00 00 00 00 00
  0000001c:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  00000038:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  00000054:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  00000070:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 4C 6F 67 20 65 6E 74 72 79 20 66 6F
  0000008c:  72 20 72 61 64 61 72 5F 32 30 32 34 2D 30 36 2D 31 38 2E 6C 6F 67 0A 54 69 6D 65 73
  000000a8:  74 61 6D 70 3A 20 32 30 32 35 2D 30 37 2D 30 39 20 32 32 3A 34 32 3A 30 33 2E 38 31
  000000c4:  31 37 39 34 0A 54 68 69 73 20 69 73 20 61 20 66 61 6B 65 20 6C 6F 67 20 65 6E 74 72
  000000e0:  79 2E                                                                              
2025-07-09 22:42:03,811 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=11
2025-07-09 22:42:03,812 - INFO - [雷达001005AD] 雷达日志下载指令设置成功
2025-07-09 22:42:03,813 - INFO - [雷达001005AD] 雷达日志下载成功
2025-07-09 22:42:03,813 - INFO - 下载日志文件结果: success
2025-07-09 22:42:03,813 - INFO - 已验证文件 '001005AD\radar_file\radar_2024-06-18.log' 创建成功且内容正确。
2025-07-09 22:42:03,813 - INFO - --- [TEST] PASSED: Log File Management ---
2025-07-09 22:42:03,813 - INFO - === TEST SUITE FOR RADAR [001005AD] COMPLETED ===
2025-07-09 22:42:03,814 - INFO - === STARTING TEST SUITE FOR RADAR [001005AD] ===
2025-07-09 22:42:03,814 - INFO - Testing: Start Work (0x00)
2025-07-09 22:42:03,814 - INFO - [雷达001005AD] 开始向雷达发送工作控制指令:开始工作
2025-07-09 22:42:03,814 - INFO - [雷达001005AD] 已将counter=12的工作控制指令放入队列:开始工作
2025-07-09 22:42:04,827 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-07-09 22:42:04,827 - INFO - [模拟器] 收到指令: cmd=0x03, ext=0x00, counter=12
2025-07-09 22:42:04,827 - INFO - [雷达001005AD] 正从('127.0.0.1', 58556)处接受头部数据，目标长度为28
2025-07-09 22:42:04,827 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到头部数据，长度为28
2025-07-09 22:42:04,827 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 03 00 01 0C 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-09 22:42:04,828 - INFO - [雷达001005AD] 从('127.0.0.1', 58556)解析得到的数据长度为0
2025-07-09 22:42:04,827 - INFO - [模拟器] 已为 counter 12 发送响应 (载荷长度: 0)
2025-07-09 22:42:04,828 - INFO - [雷达001005AD] 开始从('127.0.0.1', 58556)处接受数据，目标长度为0
2025-07-09 22:42:04,828 - INFO - [雷达001005AD] 成功在('127.0.0.1', 58556)接收到主要数据，长度为0
2025-07-09 22:42:04,828 - INFO - [雷达001005AD] 主要数据为空
2025-07-09 22:42:04,828 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=12
2025-07-09 22:42:04,830 - INFO - [雷达001005AD] 雷达开始工作
2025-07-09 22:42:04,830 - INFO - 开始工作结果: success
2025-07-09 22:42:05,332 - INFO - 主线程结束。守护线程将随之退出。
2025-07-09 23:30:37,230 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\app.py', reloading
2025-07-09 23:30:39,362 - INFO - 注册蓝图
2025-07-09 23:30:39,367 - INFO - 配置数据库并连接
2025-07-09 23:30:39,367 - INFO - 正在创建并初始化MongoDB单例客户端，URI: 'mongodb://localhost:27017/base_data'
2025-07-09 23:30:39,399 - INFO - MongoDB单例客户端已成功连接并验证。
2025-07-09 23:30:39,399 - INFO - 数据库连接成功
2025-07-09 23:30:39,399 - INFO - 获取数据库连接
2025-07-09 23:30:39,399 - INFO - 共享配置初始化完成
2025-07-09 23:30:39,409 - WARNING -  * Debugger is active!
2025-07-09 23:30:39,415 - INFO -  * Debugger PIN: 875-839-541
2025-07-09 23:30:47,928 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\app.py', reloading
2025-07-09 23:30:49,910 - INFO - 注册蓝图
2025-07-09 23:30:49,916 - INFO - 配置数据库并连接
2025-07-09 23:30:49,916 - INFO - 正在创建并初始化MongoDB单例客户端，URI: 'mongodb://localhost:27017/base_data'
2025-07-09 23:30:49,940 - INFO - MongoDB单例客户端已成功连接并验证。
2025-07-09 23:30:49,940 - INFO - 数据库连接成功
2025-07-09 23:30:49,940 - INFO - 获取数据库连接
2025-07-09 23:30:49,940 - INFO - 共享配置初始化完成
2025-07-09 23:30:49,951 - WARNING -  * Debugger is active!
2025-07-09 23:30:49,958 - INFO -  * Debugger PIN: 875-839-541
2025-07-10 00:09:33,103 - INFO - 127.0.0.1 - - [10/Jul/2025 00:09:33] "OPTIONS /user/login HTTP/1.1" 200 -
2025-07-10 00:09:33,123 - INFO - 用户24216131尝试登录
2025-07-10 00:09:33,134 - INFO - 用户24216131的密码为$2b$12$YPhNu38tGaoEu5n9cvEgC.KhfDWTxIJxDRlwNka5LbPW59mAm/zZm
2025-07-10 00:09:33,365 - INFO - 127.0.0.1 - - [10/Jul/2025 00:09:33] "POST /user/login HTTP/1.1" 200 -
2025-07-10 00:09:33,410 - INFO - 127.0.0.1 - - [10/Jul/2025 00:09:33] "OPTIONS /get_scene_list HTTP/1.1" 200 -
2025-07-10 00:09:33,422 - INFO - ['6864a5ccee5b2a0842a6518a']
2025-07-10 00:09:33,423 - INFO - 127.0.0.1 - - [10/Jul/2025 00:09:33] "POST /get_scene_list HTTP/1.1" 200 -
2025-07-10 00:09:33,535 - INFO - 127.0.0.1 - - [10/Jul/2025 00:09:33] "GET /image/static/images/shenzhen.jpg HTTP/1.1" 200 -
2025-07-10 00:09:34,360 - INFO - 127.0.0.1 - - [10/Jul/2025 00:09:34] "OPTIONS /check_all_radar HTTP/1.1" 200 -
2025-07-10 00:09:34,367 - INFO - 127.0.0.1 - - [10/Jul/2025 00:09:34] "POST /check_all_radar HTTP/1.1" 200 -
2025-07-10 00:09:58,269 - INFO - 127.0.0.1 - - [10/Jul/2025 00:09:58] "OPTIONS /user/login HTTP/1.1" 200 -
2025-07-10 00:09:58,277 - INFO - 用户24216131尝试登录
2025-07-10 00:09:58,279 - INFO - 用户24216131的密码为$2b$12$YPhNu38tGaoEu5n9cvEgC.KhfDWTxIJxDRlwNka5LbPW59mAm/zZm
2025-07-10 00:09:58,496 - INFO - 127.0.0.1 - - [10/Jul/2025 00:09:58] "POST /user/login HTTP/1.1" 200 -
2025-07-10 00:09:58,534 - INFO - 127.0.0.1 - - [10/Jul/2025 00:09:58] "OPTIONS /get_scene_list HTTP/1.1" 200 -
2025-07-10 00:09:58,540 - INFO - ['6864a5ccee5b2a0842a6518a']
2025-07-10 00:09:58,542 - INFO - 127.0.0.1 - - [10/Jul/2025 00:09:58] "POST /get_scene_list HTTP/1.1" 200 -
2025-07-10 00:09:58,587 - INFO - 127.0.0.1 - - [10/Jul/2025 00:09:58] "GET /image/static/images/shenzhen.jpg HTTP/1.1" 200 -
2025-07-10 00:09:59,473 - INFO - 127.0.0.1 - - [10/Jul/2025 00:09:59] "OPTIONS /check_all_radar HTTP/1.1" 200 -
2025-07-10 00:09:59,483 - INFO - 127.0.0.1 - - [10/Jul/2025 00:09:59] "POST /check_all_radar HTTP/1.1" 200 -
2025-07-10 07:53:42,770 - INFO - 注册蓝图
2025-07-10 07:53:42,776 - INFO - 配置数据库并连接
2025-07-10 07:53:42,777 - INFO - 正在创建并初始化MongoDB单例客户端，URI: 'mongodb://localhost:27017/base_data'
2025-07-10 07:53:42,804 - INFO - MongoDB单例客户端已成功连接并验证。
2025-07-10 07:53:42,804 - INFO - 数据库连接成功
2025-07-10 07:53:42,804 - INFO - 获取数据库连接
2025-07-10 07:53:42,804 - INFO - 共享配置初始化完成
2025-07-10 07:53:42,830 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-10 07:53:42,830 - INFO - [33mPress CTRL+C to quit[0m
2025-07-10 07:53:42,832 - INFO -  * Restarting with stat
2025-07-10 07:53:44,587 - INFO - 注册蓝图
2025-07-10 07:53:44,598 - INFO - 配置数据库并连接
2025-07-10 07:53:44,599 - INFO - 正在创建并初始化MongoDB单例客户端，URI: 'mongodb://localhost:27017/base_data'
2025-07-10 07:53:44,609 - INFO - MongoDB单例客户端已成功连接并验证。
2025-07-10 07:53:44,609 - INFO - 数据库连接成功
2025-07-10 07:53:44,609 - INFO - 获取数据库连接
2025-07-10 07:53:44,610 - INFO - 共享配置初始化完成
2025-07-10 07:53:44,636 - WARNING -  * Debugger is active!
2025-07-10 07:53:44,648 - INFO -  * Debugger PIN: 875-839-541
2025-07-10 07:54:04,068 - INFO - 127.0.0.1 - - [10/Jul/2025 07:54:04] "OPTIONS /user/login HTTP/1.1" 200 -
2025-07-10 07:54:04,075 - INFO - 用户24216131尝试登录
2025-07-10 07:54:04,080 - INFO - 用户24216131的密码为$2b$12$YPhNu38tGaoEu5n9cvEgC.KhfDWTxIJxDRlwNka5LbPW59mAm/zZm
2025-07-10 07:54:04,322 - INFO - 127.0.0.1 - - [10/Jul/2025 07:54:04] "POST /user/login HTTP/1.1" 200 -
2025-07-10 07:54:04,343 - INFO - 127.0.0.1 - - [10/Jul/2025 07:54:04] "OPTIONS /get_scene_list HTTP/1.1" 200 -
2025-07-10 07:54:04,349 - INFO - ['6864a5ccee5b2a0842a6518a']
2025-07-10 07:54:04,352 - INFO - 127.0.0.1 - - [10/Jul/2025 07:54:04] "POST /get_scene_list HTTP/1.1" 200 -
2025-07-10 07:54:04,461 - INFO - 127.0.0.1 - - [10/Jul/2025 07:54:04] "GET /image/static/images/shenzhen.jpg HTTP/1.1" 200 -
2025-07-10 07:54:05,934 - INFO - 127.0.0.1 - - [10/Jul/2025 07:54:05] "OPTIONS /check_all_radar HTTP/1.1" 200 -
2025-07-10 07:54:05,946 - INFO - 127.0.0.1 - - [10/Jul/2025 07:54:05] "POST /check_all_radar HTTP/1.1" 200 -
2025-07-10 08:18:03,197 - INFO - 127.0.0.1 - - [10/Jul/2025 08:18:03] "OPTIONS /user/login HTTP/1.1" 200 -
2025-07-10 08:18:03,201 - INFO - 用户24216131尝试登录
2025-07-10 08:18:03,202 - INFO - 用户24216131的密码为$2b$12$YPhNu38tGaoEu5n9cvEgC.KhfDWTxIJxDRlwNka5LbPW59mAm/zZm
2025-07-10 08:18:03,410 - INFO - 127.0.0.1 - - [10/Jul/2025 08:18:03] "POST /user/login HTTP/1.1" 200 -
2025-07-10 08:18:03,438 - INFO - 127.0.0.1 - - [10/Jul/2025 08:18:03] "OPTIONS /get_scene_list HTTP/1.1" 200 -
2025-07-10 08:18:03,444 - INFO - ['6864a5ccee5b2a0842a6518a']
2025-07-10 08:18:03,446 - INFO - 127.0.0.1 - - [10/Jul/2025 08:18:03] "POST /get_scene_list HTTP/1.1" 200 -
2025-07-10 08:18:03,490 - INFO - 127.0.0.1 - - [10/Jul/2025 08:18:03] "GET /image/static/images/shenzhen.jpg HTTP/1.1" 200 -
2025-07-10 08:18:05,939 - INFO - 127.0.0.1 - - [10/Jul/2025 08:18:05] "OPTIONS /check_all_radar HTTP/1.1" 200 -
2025-07-10 08:18:05,947 - INFO - 127.0.0.1 - - [10/Jul/2025 08:18:05] "POST /check_all_radar HTTP/1.1" 200 -
2025-07-10 08:27:25,281 - INFO - 127.0.0.1 - - [10/Jul/2025 08:27:25] "OPTIONS /user/login HTTP/1.1" 200 -
2025-07-10 08:27:25,287 - INFO - 用户24216131尝试登录
2025-07-10 08:27:25,288 - INFO - 用户24216131的密码为$2b$12$YPhNu38tGaoEu5n9cvEgC.KhfDWTxIJxDRlwNka5LbPW59mAm/zZm
2025-07-10 08:27:25,495 - INFO - 127.0.0.1 - - [10/Jul/2025 08:27:25] "POST /user/login HTTP/1.1" 200 -
2025-07-10 08:27:25,526 - INFO - 127.0.0.1 - - [10/Jul/2025 08:27:25] "OPTIONS /get_scene_list HTTP/1.1" 200 -
2025-07-10 08:27:25,535 - INFO - ['6864a5ccee5b2a0842a6518a']
2025-07-10 08:27:25,536 - INFO - 127.0.0.1 - - [10/Jul/2025 08:27:25] "POST /get_scene_list HTTP/1.1" 200 -
2025-07-10 08:27:25,594 - INFO - 127.0.0.1 - - [10/Jul/2025 08:27:25] "GET /image/static/images/shenzhen.jpg HTTP/1.1" 200 -
2025-07-10 08:27:26,479 - INFO - 127.0.0.1 - - [10/Jul/2025 08:27:26] "OPTIONS /check_all_radar HTTP/1.1" 200 -
2025-07-10 08:27:26,487 - INFO - 127.0.0.1 - - [10/Jul/2025 08:27:26] "POST /check_all_radar HTTP/1.1" 200 -
2025-07-10 08:38:07,630 - INFO - 127.0.0.1 - - [10/Jul/2025 08:38:07] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:38:07,631 - INFO - 127.0.0.1 - - [10/Jul/2025 08:38:07] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:38:07,635 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:38:07,635 - INFO - 127.0.0.1 - - [10/Jul/2025 08:38:07] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:38:07,636 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:38:07,637 - INFO - 127.0.0.1 - - [10/Jul/2025 08:38:07] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:38:50,141 - INFO - 127.0.0.1 - - [10/Jul/2025 08:38:50] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:38:50,145 - INFO - 127.0.0.1 - - [10/Jul/2025 08:38:50] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:38:50,146 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:38:50,146 - INFO - 127.0.0.1 - - [10/Jul/2025 08:38:50] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:38:50,149 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:38:50,150 - INFO - 127.0.0.1 - - [10/Jul/2025 08:38:50] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:38:57,025 - INFO - 127.0.0.1 - - [10/Jul/2025 08:38:57] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:38:57,029 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:38:57,029 - INFO - 127.0.0.1 - - [10/Jul/2025 08:38:57] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:38:57,033 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:38:57,033 - INFO - 127.0.0.1 - - [10/Jul/2025 08:38:57] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:39:06,416 - INFO - 127.0.0.1 - - [10/Jul/2025 08:39:06] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:39:06,422 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:39:06,422 - INFO - 127.0.0.1 - - [10/Jul/2025 08:39:06] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:39:06,423 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:39:06,424 - INFO - 127.0.0.1 - - [10/Jul/2025 08:39:06] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:41:20,540 - INFO - 127.0.0.1 - - [10/Jul/2025 08:41:20] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:41:20,541 - INFO - 127.0.0.1 - - [10/Jul/2025 08:41:20] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:41:20,546 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:41:20,548 - INFO - 127.0.0.1 - - [10/Jul/2025 08:41:20] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:41:20,550 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:41:20,550 - INFO - 127.0.0.1 - - [10/Jul/2025 08:41:20] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:41:59,214 - INFO - 127.0.0.1 - - [10/Jul/2025 08:41:59] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:41:59,216 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:41:59,217 - INFO - 127.0.0.1 - - [10/Jul/2025 08:41:59] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:41:59,218 - INFO - 127.0.0.1 - - [10/Jul/2025 08:41:59] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:41:59,220 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:41:59,221 - INFO - 127.0.0.1 - - [10/Jul/2025 08:41:59] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:42:18,621 - INFO - 127.0.0.1 - - [10/Jul/2025 08:42:18] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:42:18,628 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:42:18,628 - INFO - 127.0.0.1 - - [10/Jul/2025 08:42:18] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:42:18,630 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:42:18,630 - INFO - 127.0.0.1 - - [10/Jul/2025 08:42:18] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:42:22,752 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:42:22,753 - INFO - 127.0.0.1 - - [10/Jul/2025 08:42:22] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:42:22,762 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:42:22,763 - INFO - 127.0.0.1 - - [10/Jul/2025 08:42:22] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:42:36,381 - INFO - 127.0.0.1 - - [10/Jul/2025 08:42:36] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:42:36,384 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:42:36,385 - INFO - 127.0.0.1 - - [10/Jul/2025 08:42:36] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:42:36,385 - INFO - 127.0.0.1 - - [10/Jul/2025 08:42:36] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:42:36,388 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:42:36,388 - INFO - 127.0.0.1 - - [10/Jul/2025 08:42:36] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:42:48,212 - INFO - 127.0.0.1 - - [10/Jul/2025 08:42:48] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:42:48,213 - INFO - 127.0.0.1 - - [10/Jul/2025 08:42:48] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:42:48,214 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:42:48,215 - INFO - 127.0.0.1 - - [10/Jul/2025 08:42:48] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:42:48,217 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:42:48,217 - INFO - 127.0.0.1 - - [10/Jul/2025 08:42:48] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:42:49,494 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:42:49,495 - INFO - 127.0.0.1 - - [10/Jul/2025 08:42:49] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:42:49,496 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:42:49,496 - INFO - 127.0.0.1 - - [10/Jul/2025 08:42:49] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:42:50,493 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:42:50,494 - INFO - 127.0.0.1 - - [10/Jul/2025 08:42:50] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:42:50,498 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:42:50,498 - INFO - 127.0.0.1 - - [10/Jul/2025 08:42:50] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:43:36,716 - INFO - 127.0.0.1 - - [10/Jul/2025 08:43:36] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:43:36,719 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:43:36,719 - INFO - 127.0.0.1 - - [10/Jul/2025 08:43:36] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:43:36,719 - INFO - 127.0.0.1 - - [10/Jul/2025 08:43:36] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:43:36,723 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:43:36,723 - INFO - 127.0.0.1 - - [10/Jul/2025 08:43:36] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:43:44,299 - INFO - 127.0.0.1 - - [10/Jul/2025 08:43:44] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:43:44,302 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:43:44,302 - INFO - 127.0.0.1 - - [10/Jul/2025 08:43:44] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:43:44,303 - INFO - 127.0.0.1 - - [10/Jul/2025 08:43:44] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 08:43:44,308 - WARNING - Validation chain failed: [REQUIRED_FIELD_MISSING] - radar_ID不能缺失 - 400
2025-07-10 08:43:44,308 - INFO - 127.0.0.1 - - [10/Jul/2025 08:43:44] "[31m[1mPOST /radar_information/update_radar_information HTTP/1.1[0m" 400 -
2025-07-10 08:44:41,585 - INFO - 注册蓝图
2025-07-10 08:44:41,590 - INFO - 配置数据库并连接
2025-07-10 08:44:41,590 - INFO - 正在创建并初始化MongoDB单例客户端，URI: 'mongodb://localhost:27017/base_data'
2025-07-10 08:44:41,615 - INFO - MongoDB单例客户端已成功连接并验证。
2025-07-10 08:44:41,616 - INFO - 数据库连接成功
2025-07-10 08:44:41,616 - INFO - 获取数据库连接
2025-07-10 08:44:41,616 - INFO - 共享配置初始化完成
2025-07-10 08:44:41,635 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-10 08:44:41,635 - INFO - [33mPress CTRL+C to quit[0m
2025-07-10 08:44:41,636 - INFO -  * Restarting with stat
2025-07-10 08:44:42,663 - INFO - 注册蓝图
2025-07-10 08:44:42,667 - INFO - 配置数据库并连接
2025-07-10 08:44:42,667 - INFO - 正在创建并初始化MongoDB单例客户端，URI: 'mongodb://localhost:27017/base_data'
2025-07-10 08:44:42,682 - INFO - MongoDB单例客户端已成功连接并验证。
2025-07-10 08:44:42,682 - INFO - 数据库连接成功
2025-07-10 08:44:42,682 - INFO - 获取数据库连接
2025-07-10 08:44:42,682 - INFO - 共享配置初始化完成
2025-07-10 08:44:42,691 - WARNING -  * Debugger is active!
2025-07-10 08:44:42,695 - INFO -  * Debugger PIN: 875-839-541
2025-07-10 08:45:16,352 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\my_code\\radar_code.py', reloading
2025-07-10 08:45:17,055 - INFO -  * Restarting with stat
2025-07-10 08:49:15,708 - INFO -  * Restarting with stat
2025-07-10 08:49:39,681 - INFO -  * Restarting with stat
2025-07-10 08:50:01,828 - INFO -  * Restarting with stat
2025-07-10 08:50:37,535 - INFO -  * Restarting with stat
2025-07-10 08:50:43,974 - INFO -  * Restarting with stat
2025-07-10 08:50:51,427 - INFO -  * Restarting with stat
2025-07-10 08:50:54,720 - INFO -  * Restarting with stat
2025-07-10 08:50:58,767 - INFO -  * Restarting with stat
2025-07-10 08:51:02,163 - INFO -  * Restarting with stat
2025-07-10 08:51:05,625 - INFO -  * Restarting with stat
2025-07-10 08:51:08,811 - INFO -  * Restarting with stat
2025-07-10 08:51:27,381 - INFO -  * Restarting with stat
2025-07-10 10:15:52,845 - INFO -  * Restarting with stat
2025-07-10 11:11:49,184 - INFO - 注册蓝图
2025-07-10 11:11:49,190 - INFO - 配置数据库并连接
2025-07-10 11:11:49,191 - INFO - 正在创建并初始化MongoDB单例客户端，URI: 'mongodb://localhost:27017/base_data'
2025-07-10 11:11:49,196 - INFO - MongoDB单例客户端已成功连接并验证。
2025-07-10 11:11:49,196 - INFO - 数据库连接成功
2025-07-10 11:11:49,196 - INFO - 获取数据库连接
2025-07-10 11:11:49,196 - INFO - 共享配置初始化完成
2025-07-10 11:11:49,210 - WARNING -  * Debugger is active!
2025-07-10 11:11:49,216 - INFO -  * Debugger PIN: 875-839-541
2025-07-10 11:11:51,871 - INFO - 127.0.0.1 - - [10/Jul/2025 11:11:51] "OPTIONS /listen_radar_state HTTP/1.1" 200 -
2025-07-10 11:11:51,877 - INFO - 127.0.0.1 - - [10/Jul/2025 11:11:51] "POST /listen_radar_state HTTP/1.1" 200 -
2025-07-10 11:11:56,894 - INFO - 127.0.0.1 - - [10/Jul/2025 11:11:56] "OPTIONS /listen_radar_state HTTP/1.1" 200 -
2025-07-10 11:11:56,899 - INFO - 127.0.0.1 - - [10/Jul/2025 11:11:56] "POST /listen_radar_state HTTP/1.1" 200 -
2025-07-10 11:12:01,231 - INFO - 127.0.0.1 - - [10/Jul/2025 11:12:01] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 11:12:01,232 - INFO - 127.0.0.1 - - [10/Jul/2025 11:12:01] "OPTIONS /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 11:12:01,245 - INFO - 127.0.0.1 - - [10/Jul/2025 11:12:01] "POST /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 11:12:01,248 - INFO - 127.0.0.1 - - [10/Jul/2025 11:12:01] "POST /radar_information/update_radar_information HTTP/1.1" 200 -
2025-07-10 11:12:06,179 - INFO - 127.0.0.1 - - [10/Jul/2025 11:12:06] "OPTIONS /listen_radar_state HTTP/1.1" 200 -
2025-07-10 11:12:06,184 - INFO - 127.0.0.1 - - [10/Jul/2025 11:12:06] "POST /listen_radar_state HTTP/1.1" 200 -
2025-07-10 11:12:11,179 - INFO - 127.0.0.1 - - [10/Jul/2025 11:12:11] "OPTIONS /listen_radar_state HTTP/1.1" 200 -
2025-07-10 11:12:11,185 - INFO - 127.0.0.1 - - [10/Jul/2025 11:12:11] "POST /listen_radar_state HTTP/1.1" 200 -
2025-07-10 11:12:16,192 - INFO - 127.0.0.1 - - [10/Jul/2025 11:12:16] "OPTIONS /listen_radar_state HTTP/1.1" 200 -
2025-07-10 11:12:16,197 - INFO - 127.0.0.1 - - [10/Jul/2025 11:12:16] "POST /listen_radar_state HTTP/1.1" 200 -
2025-07-10 11:12:21,219 - INFO - 127.0.0.1 - - [10/Jul/2025 11:12:21] "OPTIONS /listen_radar_state HTTP/1.1" 200 -
2025-07-10 11:12:21,224 - INFO - 127.0.0.1 - - [10/Jul/2025 11:12:21] "POST /listen_radar_state HTTP/1.1" 200 -
2025-07-10 11:12:26,230 - INFO - 127.0.0.1 - - [10/Jul/2025 11:12:26] "OPTIONS /listen_radar_state HTTP/1.1" 200 -
2025-07-10 11:12:26,234 - INFO - 127.0.0.1 - - [10/Jul/2025 11:12:26] "POST /listen_radar_state HTTP/1.1" 200 -
2025-07-10 11:12:30,397 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\my_code\\radar_code.py', reloading
